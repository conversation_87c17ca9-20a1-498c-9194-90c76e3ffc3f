-- SQL script to identify tables and columns that might contain phone numbers
-- Version 2: More flexible approach that doesn't assume specific column names

-- Check information schema for columns with names suggesting phone numbers
SELECT 
    table_schema,
    table_name,
    column_name,
    data_type
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND (
        column_name ILIKE '%phone%' OR
        column_name ILIKE '%mobile%' OR
        column_name ILIKE '%cell%' OR
        column_name ILIKE '%contact%' OR
        column_name ILIKE '%tel%'
    )
ORDER BY 
    table_name, column_name;

-- Get all column names from the profiles table
SELECT 
    column_name,
    data_type
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND table_name = 'profiles'
ORDER BY 
    ordinal_position;

-- Sample data from profiles table (dynamically selecting columns)
SELECT 
    id, 
    full_name,
    email,
    -- Only include phone-related columns that actually exist
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'phone'
    ) THEN phone ELSE NULL END as phone,
    -- Add other potential phone fields here with similar CASE statements if needed
    created_at
FROM 
    profiles
LIMIT 5;

-- Get all column names from the orders table
SELECT 
    column_name,
    data_type
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND table_name = 'orders'
ORDER BY 
    ordinal_position;

-- Sample data from orders table (focusing on potential phone fields)
SELECT 
    id,
    user_id,
    -- Only include phone-related columns that actually exist
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'orders' AND column_name = 'delivery_phone'
    ) THEN delivery_phone ELSE NULL END as delivery_phone,
    -- Add other potential phone fields here with similar CASE statements if needed
    created_at
FROM 
    orders
LIMIT 5;

-- Get all column names from the addresses table
SELECT 
    column_name,
    data_type
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND table_name = 'addresses'
ORDER BY 
    ordinal_position;

-- Sample data from addresses table (focusing on potential phone fields)
SELECT 
    id,
    user_id,
    -- Only include phone-related columns that actually exist
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'addresses' AND column_name = 'phone'
    ) THEN phone ELSE NULL END as phone,
    -- Add other potential phone fields here with similar CASE statements if needed
    created_at
FROM 
    addresses
LIMIT 5;

-- Join orders with profiles to see the relationship
SELECT 
    o.id as order_id,
    o.user_id,
    p.id as profile_id,
    p.full_name,
    p.email,
    -- Only include phone-related columns that actually exist
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'phone'
    ) THEN p.phone ELSE NULL END as profile_phone,
    -- Only include delivery_phone if it exists
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'orders' AND column_name = 'delivery_phone'
    ) THEN o.delivery_phone ELSE NULL END as order_phone
FROM 
    orders o
LEFT JOIN 
    profiles p ON o.user_id = p.id
LIMIT 5;

-- Check if the orders table has an address_id column
SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' AND table_name = 'orders' AND column_name = 'address_id'
) as has_address_id;

-- Join orders with addresses to see the relationship (only if address_id exists)
WITH check_columns AS (
    SELECT 
        EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = 'orders' AND column_name = 'address_id'
        ) as has_address_id,
        EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = 'addresses' AND column_name = 'phone'
        ) as has_address_phone
)
SELECT 
    o.id as order_id,
    CASE WHEN c.has_address_id THEN o.address_id ELSE NULL END as address_id,
    a.id as address_table_id,
    a.user_id,
    CASE WHEN c.has_address_phone THEN a.phone ELSE NULL END as address_phone,
    -- Only include delivery_phone if it exists
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'orders' AND column_name = 'delivery_phone'
    ) THEN o.delivery_phone ELSE NULL END as order_phone
FROM 
    orders o
CROSS JOIN
    check_columns c
LEFT JOIN 
    addresses a ON c.has_address_id AND o.address_id = a.id
LIMIT 5;
