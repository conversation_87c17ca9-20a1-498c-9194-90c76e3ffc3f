import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { View, Text, StyleSheet } from 'react-native';

// Import constants
import { COLORS } from '../constants/theme';
import { SCREENS } from '../constants';

// Import screens
import TestScreen from '../screens/TestScreen';
import WelcomeScreen from '../screens/WelcomeScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';

// Import customer screens
import HomeScreen from '../screens/customer/HomeScreen';
import ProductDetailsScreen from '../screens/customer/ProductDetailsScreen';
import CartScreen from '../screens/customer/cart/CartScreen';
import ProfileScreen from '../screens/customer/profile/ProfileScreen';
import CategoriesScreen from '../screens/customer/CategoriesScreen';
import CheckoutScreen from '../screens/customer/cart/CheckoutScreen';
import OrdersScreen from '../screens/customer/orders/OrdersScreen';
import OrderDetailsScreen from '../screens/customer/orders/OrderDetailsScreen';
import ProductListScreen from '../screens/customer/ProductListScreen';

// Import store
import useStore from '../store/useStore';

// These screens will be implemented later
const FarmerProfileScreen = () => (
  <View style={styles.screenContainer}>
    <Text style={styles.title}>Farmer Profile Screen</Text>
  </View>
);

const SearchScreen = () => (
  <View style={styles.screenContainer}>
    <Text style={styles.title}>Search Screen</Text>
  </View>
);

const NotificationsScreen = () => (
  <View style={styles.screenContainer}>
    <Text style={styles.title}>Notifications Screen</Text>
  </View>
);

// Styles for placeholder screens
const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: COLORS.primary,
  },
});

// Create navigators
const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

// Bottom Tab Navigator
const BottomTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: COLORS.primary,
        tabBarInactiveTintColor: COLORS.gray,
        tabBarStyle: {
          height: 60,
          paddingBottom: 10,
          paddingTop: 5,
        },
        headerShown: false,
      }}
    >
      <Tab.Screen
        name={SCREENS.HOME}
        component={HomeScreen}
        options={{
          tabBarLabel: 'Home',
          tabBarIcon: ({ color, size }) => (
            <MaterialCommunityIcons name="home" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name={SCREENS.CATEGORY}
        component={CategoriesScreen}
        options={{
          tabBarLabel: 'Categories',
          tabBarIcon: ({ color, size }) => (
            <MaterialCommunityIcons name="view-grid" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name={SCREENS.CART}
        component={CartScreen}
        options={{
          tabBarLabel: 'Cart',
          tabBarIcon: ({ color, size }) => (
            <MaterialCommunityIcons name="cart" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name={SCREENS.PROFILE}
        component={ProfileScreen}
        options={{
          tabBarLabel: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <MaterialCommunityIcons name="account" color={color} size={size} />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

// Auth Navigator
const AuthNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="Test" component={TestScreen} />
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      <Stack.Screen name={SCREENS.LOGIN} component={LoginScreen} />
      <Stack.Screen name={SCREENS.REGISTER} component={RegisterScreen} />
      <Stack.Screen name={SCREENS.FORGOT_PASSWORD} component={ForgotPasswordScreen} />
    </Stack.Navigator>
  );
};

// Main App Navigator
const AppNavigator = () => {
  const { isAuthenticated, isAdmin } = useStore();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      {!isAuthenticated ? (
        <Stack.Screen name="Auth" component={AuthNavigator} />
      ) : isAdmin ? (
        // Admin flow
        <Stack.Screen name="Admin" component={AdminNavigator} />
      ) : (
        // Customer flow
        <>
          <Stack.Screen name="Main" component={BottomTabNavigator} />
          <Stack.Screen name={SCREENS.PRODUCT_DETAILS} component={ProductDetailsScreen} />
          <Stack.Screen name={SCREENS.CATEGORY} component={ProductListScreen} />
          <Stack.Screen name={SCREENS.CHECKOUT} component={CheckoutScreen} />
          <Stack.Screen name={SCREENS.ORDERS} component={OrdersScreen} />
          <Stack.Screen name={SCREENS.ORDER_DETAILS} component={OrderDetailsScreen} />
          <Stack.Screen name={SCREENS.FARMER_PROFILE} component={FarmerProfileScreen} />
          <Stack.Screen name={SCREENS.SEARCH} component={SearchScreen} />
          <Stack.Screen name={SCREENS.NOTIFICATIONS} component={NotificationsScreen} />
        </>
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
