import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import useAuthStore from './SimpleAuthStore';
import SimpleNavigation from './SimpleNavigation';
import SimpleHome from './SimpleHome';

export default function AuthWrapper() {
  const { isAuthenticated, isLoading, user } = useAuthStore();
  const [initializing, setInitializing] = useState(true);

  useEffect(() => {
    // Simulate checking for stored authentication
    const timer = setTimeout(() => {
      setInitializing(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (initializing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading Zakumunda...</Text>
      </View>
    );
  }

  // If authenticated, show the main app
  if (isAuthenticated) {
    return <SimpleHome />;
  }

  // If not authenticated, show the auth flow
  return <SimpleNavigation />;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#4CAF50',
  },
});
