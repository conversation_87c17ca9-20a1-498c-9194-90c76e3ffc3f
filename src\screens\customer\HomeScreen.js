import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  Button,
  ScrollView,
  Image,
  Dimensions,
  TouchableOpacity,
  FlatList,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import useStore from '../../store/useStore';
import Toast from '../../components/Toast';
import { getProducts } from '../../lib/supabase';

const { width: screenWidth } = Dimensions.get('window');

// Promotional banners
const promotions = [
  {
    id: '1',
    title: 'Fresh Harvest Sale',
    subtitle: 'Up to 30% off on fresh vegetables',
    image: 'https://images.unsplash.com/photo-1488459716781-31db52582fe9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    color: '#2E7D32',
  },
  {
    id: '2',
    title: 'Organic Fruits',
    subtitle: 'Freshly picked from local farms',
    image: 'https://images.unsplash.com/photo-1519996529931-28324d5a630e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    color: '#8BC34A',
  },
  {
    id: '3',
    title: 'Free Delivery',
    subtitle: 'On orders above $20',
    image: 'https://images.unsplash.com/photo-1542838132-92c53300491e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80',
    color: '#4CAF50',
  },
];

const HomeScreen = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const { addToCart, user } = useStore();
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [recentProducts, setRecentProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch products and categories on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch featured products
        const { data: featuredData, error: featuredError } = await getProducts({
          is_featured: true,
          limit: 10
        });

        if (featuredError) throw featuredError;
        setFeaturedProducts(featuredData || []);

        // Fetch recent products (for now, just use non-featured products)
        const { data: recentData, error: recentError } = await getProducts({
          limit: 5,
          sort_by: 'created_at',
          sort_order: 'desc'
        });

        if (recentError) throw recentError;
        setRecentProducts(recentData?.slice(0, 3) || []);

        // Get unique categories from products
        if (featuredData) {
          const uniqueCategories = [...new Set(featuredData
            .filter(product => product.categories)
            .map(product => JSON.stringify({
              id: product.categories.id,
              name: product.categories.name
            }))
          )].map(cat => JSON.parse(cat));

          setCategories(uniqueCategories);
        }

      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      alert(`Searching for: ${searchQuery}`);
      // In a real app, you would navigate to search results
      // navigation.navigate('Search', { query: searchQuery });
    }
  };

  const handleAddToCart = (product) => {
    // Format the product to match the expected structure in the cart
    const cartProduct = {
      id: product.id,
      name: product.name,
      price: parseFloat(product.price),
      image: product.image_url,
      unit: product.unit,
      quantity: 1,
      farmer: {
        id: product.farmer_id,
        name: product.farmers?.business_name || 'Local Farm'
      }
    };

    addToCart(cartProduct, 1); // Add 1 quantity of the product
    setToastMessage(`${product.name} added to cart`);
    setToastVisible(true);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        <View style={styles.header}>
          {/* Header with Logo, Search, and Icons */}
          <View style={styles.headerRow}>
            <View style={styles.logoContainer}>
              <Text style={styles.logoText}>Z</Text>
            </View>

            {/* Search Bar */}
            <View style={styles.searchContainer}>
              <TextInput
                style={styles.searchInput}
                placeholder="Search for fruits, vegetables..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                onSubmitEditing={handleSearch}
              />
              <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
                <MaterialCommunityIcons name="magnify" size={22} color="#2E7D32" />
              </TouchableOpacity>
            </View>

            <View style={styles.rightIcons}>
              <TouchableOpacity style={styles.iconButton} onPress={() => alert('Messages')}>
                <MaterialCommunityIcons name="message-outline" size={22} color="#2E7D32" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.iconButton} onPress={() => alert('Notifications')}>
                <MaterialCommunityIcons name="bell-outline" size={22} color="#2E7D32" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Welcome Message */}
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>{getGreeting()}, {user?.full_name || 'Guest'}!</Text>
          <Text style={styles.welcomeSubtext}>Find fresh produce for your needs</Text>
        </View>

        {/* Section Divider */}
        <View style={styles.divider} />

        {/* Promotions Horizontal Scroll */}
        <View style={styles.carouselContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Promotions</Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.promotionsScroll}
          >
            {promotions.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={[styles.carouselItem, { backgroundColor: item.color }]}
                onPress={() => navigation.navigate('Category')}
              >
                <Image source={{ uri: item.image }} style={styles.carouselImage} />
                <View style={styles.carouselContent}>
                  <Text style={styles.carouselTitle}>{item.title}</Text>
                  <Text style={styles.carouselSubtitle}>{item.subtitle}</Text>
                  <Button
                    title="Shop Now"
                    onPress={() => navigation.navigate('Category')}
                  />
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Section Divider */}
        <View style={styles.divider} />

        {/* Categories */}
        <View style={styles.categoriesContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Categories</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Category')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          {loading ? (
            <ActivityIndicator size="small" color="#2E7D32" style={{ marginVertical: 10 }} />
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesScroll}
            >
              <TouchableOpacity
                style={[styles.categoryPill, { backgroundColor: '#EFF7F0' }]}
                onPress={() => navigation.navigate('Category')}
              >
                <Text style={styles.categoryPillText}>All</Text>
              </TouchableOpacity>

              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[styles.categoryPill, { backgroundColor: '#FFFFFF', borderColor: '#E5E5E5' }]}
                  onPress={() => navigation.navigate('Category', { categoryId: category.id })}
                >
                  <Text style={styles.categoryPillText}>{category.name}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>

        {/* Section Divider */}
        <View style={styles.divider} />

        {/* Featured Products */}
        <View style={styles.productsContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Products</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Category')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          {loading ? (
            <ActivityIndicator size="large" color="#2E7D32" style={{ marginVertical: 20 }} />
          ) : error ? (
            <Text style={styles.errorText}>Error loading products: {error}</Text>
          ) : (
            <FlatList
              data={featuredProducts}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              ListEmptyComponent={
                <Text style={styles.emptyText}>No featured products available</Text>
              }
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.productCard}
                  onPress={() => navigation.navigate('ProductDetails', { productId: item.id })}
                >
                  <Image
                    source={{ uri: item.image_url || 'https://via.placeholder.com/300?text=No+Image' }}
                    style={styles.productImage}
                    onError={() => console.log('Image failed to load')}
                  />
                  <View style={styles.productInfo}>
                    <Text style={styles.productName}>{item.name}</Text>
                    <Text style={styles.productPrice}>${parseFloat(item.price).toFixed(2)} / {item.unit}</Text>
                    <View style={styles.productFooter}>
                      <Text style={styles.farmerName}>{item.farmers?.business_name || 'Local Farm'}</Text>
                      <TouchableOpacity
                        style={styles.addToCartButton}
                        onPress={(e) => {
                          e.stopPropagation();
                          handleAddToCart(item);
                        }}
                      >
                        <MaterialCommunityIcons name="cart-plus" size={18} color="#fff" />
                      </TouchableOpacity>
                    </View>
                  </View>
                </TouchableOpacity>
              )}
            />
          )}
        </View>

        {/* Section Divider */}
        <View style={styles.divider} />

        {/* Special Offers */}
        <View style={styles.specialOffersContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Special Offers</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Category')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.specialOfferCard}>
            <View style={styles.specialOfferContent}>
              <Text style={styles.specialOfferTitle}>Weekend Sale</Text>
              <Text style={styles.specialOfferSubtitle}>Get 20% off on all vegetables</Text>
              <TouchableOpacity style={styles.specialOfferButton} onPress={() => navigation.navigate('Category')}>
                <Text style={styles.specialOfferButtonText}>Shop Now</Text>
              </TouchableOpacity>
            </View>
            <Image
              source={{ uri: 'https://images.unsplash.com/photo-1557844352-761f2565b576?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80' }}
              style={styles.specialOfferImage}
            />
          </View>
        </View>

        {/* Section Divider */}
        <View style={styles.divider} />

        {/* Recently Viewed */}
        <View style={styles.recentlyViewedContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recently Viewed</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Category')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          {loading ? (
            <ActivityIndicator size="small" color="#2E7D32" style={{ marginVertical: 10 }} />
          ) : error ? (
            <Text style={styles.errorText}>Error loading products: {error}</Text>
          ) : (
            <FlatList
              data={recentProducts}
              keyExtractor={(item) => `recent-${item.id}`}
              horizontal
              showsHorizontalScrollIndicator={false}
              ListEmptyComponent={
                <Text style={styles.emptyText}>No recent products available</Text>
              }
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.recentProductCard}
                  onPress={() => navigation.navigate('ProductDetails', { productId: item.id })}
                >
                  <Image
                    source={{ uri: item.image_url || 'https://via.placeholder.com/150?text=No+Image' }}
                    style={styles.recentProductImage}
                    onError={() => console.log('Image failed to load')}
                  />
                  <View style={styles.recentProductInfo}>
                    <Text style={styles.recentProductName} numberOfLines={1}>{item.name}</Text>
                    <Text style={styles.recentProductPrice}>${parseFloat(item.price).toFixed(2)}</Text>
                    <TouchableOpacity
                      style={styles.smallAddToCartButton}
                      onPress={(e) => {
                        e.stopPropagation();
                        handleAddToCart(item);
                      }}
                    >
                      <MaterialCommunityIcons name="cart-plus" size={14} color="#fff" />
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              )}
            />
          )}
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Toast Notification */}
      {toastVisible && (
        <Toast
          message={toastMessage}
          onHide={() => setToastVisible(false)}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    paddingTop: 10,
  },
  welcomeContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10, // Reduced from 15
  },
  welcomeText: {
    fontSize: 18, // Reduced from 22
    fontWeight: 'bold',
    color: '#333',
  },
  welcomeSubtext: {
    fontSize: 14, // Reduced from 16
    color: '#757575',
    marginTop: 5,
  },
  header: {
    padding: 10,
    paddingTop: 20,
    marginTop: 10,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  logoContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  rightIcons: {
    flexDirection: 'row',
  },
  iconButton: {
    marginLeft: 8,
    padding: 4,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: 10,
    height: 36,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
  },
  searchInput: {
    flex: 1,
    paddingHorizontal: 10,
    fontSize: 14,
  },
  searchButton: {
    padding: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  viewAllText: {
    fontSize: 14,
    color: '#2E7D32',
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 15,
    marginHorizontal: 20,
  },
  bottomSpacing: {
    height: 20,
  },
  specialOffersContainer: {
    paddingHorizontal: 20,
    marginVertical: 10,
  },
  specialOfferCard: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  specialOfferContent: {
    flex: 1,
    padding: 15,
    justifyContent: 'center',
  },
  specialOfferTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  specialOfferSubtitle: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 15,
  },
  specialOfferButton: {
    backgroundColor: '#2E7D32',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
    alignSelf: 'flex-start',
  },
  specialOfferButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  specialOfferImage: {
    width: 120,
    height: '100%',
  },
  recentlyViewedContainer: {
    paddingHorizontal: 20,
    marginVertical: 10,
  },
  recentProductCard: {
    width: 120,
    marginRight: 12,
    backgroundColor: '#fff',
    borderRadius: 10,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  recentProductImage: {
    width: '100%',
    height: 100,
  },
  recentProductInfo: {
    padding: 8,
    position: 'relative',
    paddingBottom: 20,
  },
  recentProductName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  recentProductPrice: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  smallAddToCartButton: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    backgroundColor: '#2E7D32',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginVertical: 10,
    fontSize: 14,
  },
  emptyText: {
    color: '#757575',
    textAlign: 'center',
    marginVertical: 10,
    fontSize: 14,
    width: screenWidth - 40,
  },
  carouselContainer: {
    marginVertical: 15,
    paddingHorizontal: 20,
  },
  promotionsScroll: {
    paddingBottom: 10,
    paddingTop: 5,
  },
  carouselItem: {
    width: 320,
    height: 180,
    borderRadius: 15,
    overflow: 'hidden',
    flexDirection: 'row',
    marginRight: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  carouselImage: {
    width: '50%',
    height: '100%',
  },
  carouselContent: {
    flex: 1,
    padding: 15,
    justifyContent: 'center',
  },
  carouselTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  carouselSubtitle: {
    fontSize: 14,
    color: '#fff',
    marginBottom: 15,
  },
  buttonContainer: {
    padding: 20,
    gap: 10,
  },
  productsContainer: {
    marginVertical: 10,
    paddingHorizontal: 20,
  },
  productCard: {
    width: 150,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: 110,
  },
  productInfo: {
    padding: 10,
  },
  productName: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  productPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 8,
  },
  productFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  farmerName: {
    fontSize: 11,
    color: '#757575',
    flex: 1,
  },
  addToCartButton: {
    backgroundColor: '#2E7D32',
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoriesContainer: {
    marginVertical: 10,
    paddingHorizontal: 20,
  },
  categoriesScroll: {
    paddingVertical: 10,
  },
  categoryPill: {
    height: 40,
    paddingHorizontal: 20,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E8E8E8',
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryPillText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2E7D32',
  },
});

export default HomeScreen;
