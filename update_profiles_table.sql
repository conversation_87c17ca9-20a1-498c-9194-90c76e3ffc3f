-- Check if phone column exists in profiles table
DO $$
BEGIN
    -- Check if the phone column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'profiles'
        AND column_name = 'phone'
    ) THEN
        -- Add phone column if it doesn't exist
        ALTER TABLE profiles ADD COLUMN phone TEXT;
        RAISE NOTICE 'Added phone column to profiles table';
    ELSE
        RAISE NOTICE 'Phone column already exists in profiles table';
    END IF;
END $$;

-- Update existing profiles to copy phone from user metadata
CREATE OR REPLACE FUNCTION update_profiles_with_phone()
RETURNS void AS $$
DECLARE
    profile_record RECORD;
    user_metadata JSONB;
BEGIN
    FOR profile_record IN SELECT id FROM profiles WHERE phone IS NULL LOOP
        -- Get user metadata from auth.users
        SELECT raw_user_meta_data INTO user_metadata
        FROM auth.users
        WHERE id = profile_record.id;
        
        -- Update profile with phone from metadata if available
        IF user_metadata ? 'phone' THEN
            UPDATE profiles
            SET phone = user_metadata->>'phone'
            WHERE id = profile_record.id;
            
            RAISE NOTICE 'Updated phone for profile %', profile_record.id;
        ELSE
            RAISE NOTICE 'No phone in metadata for profile %', profile_record.id;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
