import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  TextInput,
  ScrollView,
} from 'react-native';
import { Card, Title, IconButton, Chip, Badge } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute, CommonActions } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS, CATEGORIES, SAMPLE_PRODUCTS } from '../../constants';
import useStore from '../../store/useStore';
import Toast from '../../components/Toast';

const CategoriesScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { filterProducts, logInitialProducts, addToCart } = useStore();

  // State for search and filtering
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [filteredProducts, setFilteredProducts] = useState(SAMPLE_PRODUCTS);
  const [showCategories, setShowCategories] = useState(false);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  // Log products on mount
  useEffect(() => {
    console.log('CategoriesScreen mounted');
    const products = logInitialProducts();
    console.log('Products loaded from store:', products?.length || 0);
  }, []);

  // Get category ID from route params if available
  useEffect(() => {
    if (route.params?.categoryId) {
      const category = CATEGORIES.find(cat => cat.id === route.params.categoryId);
      if (category) {
        setSelectedCategory(category);
      }
    }
  }, [route.params]);

  // Filter products based on selected category and search query
  useEffect(() => {
    let filtered = SAMPLE_PRODUCTS;

    // Filter by category if selected
    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory.id);
    }

    // Filter by search query if provided
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredProducts(filtered);
  }, [selectedCategory, searchQuery]);

  // Handle category selection
  const handleCategoryPress = (category) => {
    setSelectedCategory(category);
    setShowCategories(false);
  };

  // Handle back button press
  const handleBackPress = () => {
    if (!showCategories && selectedCategory) {
      setShowCategories(true);
      setSelectedCategory(null);
    } else {
      navigation.goBack();
    }
  };

  const handleAddToCart = (e, item) => {
    e.stopPropagation();
    addToCart(item, 1); // Add 1 quantity of the item
    setToastMessage(`${item.name} added to cart`);
    setToastVisible(true);
  };

  // Render category item
  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(item)}
    >
      <View style={styles.categoryCard}>
        <View style={styles.categoryIconContainer}>
          <MaterialCommunityIcons
            name={item.icon}
            size={30}
            color={COLORS.white}
          />
        </View>
        <Text style={styles.categoryName}>{item.name}</Text>
      </View>
    </TouchableOpacity>
  );

  // Render product item
  const renderProductItem = ({ item }) => {
    const navigateToDetails = () => {
      // Add extensive debugging
      console.log('=== NAVIGATION DEBUGGING ===');
      console.log('Product being clicked:', item);
      console.log('Product ID:', item.id);
      console.log('Attempting to navigate to ProductDetails');
      
      // Add a 1-second delay before navigation to make sure logs appear first
      setTimeout(() => {
        // Simple direct navigation
        navigation.navigate('ProductDetails', { 
          productId: item.id,
          test: 'test-param' // Test parameter to check if navigation works
        });
      }, 1000);
    };

    return (
      <TouchableOpacity
        style={styles.productItem}
        onPress={navigateToDetails}
        activeOpacity={0.7}
      >
        <View style={styles.productCard}>
          <Image source={{ uri: item.image }} style={styles.productImage} />
          <View style={styles.productContent}>
            <Text style={styles.productName} numberOfLines={1}>{item.name}</Text>
            <Text style={styles.productPrice}>${item.price.toFixed(2)} / {item.unit}</Text>
            <View style={styles.productFooter}>
              <Text style={styles.farmerName}>{item.farmer.name}</Text>
              <TouchableOpacity
                style={styles.addToCartButton}
                onPress={(e) => handleAddToCart(e, item)}
              >
                <MaterialCommunityIcons name="cart-plus" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.white} />

      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={handleBackPress}
          color={COLORS.text}
        />
        <Text style={styles.headerTitle}>
          {showCategories ? 'Categories' : selectedCategory?.name || 'Products'}
        </Text>
        <IconButton
          icon="magnify"
          size={24}
          onPress={() => {}}
          color={COLORS.text}
        />
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <MaterialCommunityIcons name="magnify" size={22} color={COLORS.gray} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search products..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={COLORS.gray}
        />
        {searchQuery ? (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => setSearchQuery('')}
          >
            <MaterialCommunityIcons name="close-circle" size={20} color={COLORS.gray} />
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Category Tabs */}
      {!showCategories && (
        <View style={styles.categoriesNewContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoryCardsContainer}
          >
            <TouchableOpacity
              style={[
                styles.categoryCard3D,
                !selectedCategory && styles.categoryCard3DActive
              ]}
              onPress={() => setSelectedCategory(null)}
            >
              <View style={styles.categoryCardContent}>
                <View style={styles.categoryCardIconBox}>
                  <MaterialCommunityIcons 
                    name="apps" 
                    size={16} 
                    color={!selectedCategory ? COLORS.white : COLORS.primary} 
                  />
                </View>
                <Text style={[
                  styles.categoryCardTitle,
                  !selectedCategory && styles.categoryCardTitleActive
                ]}>All Products</Text>
              </View>
            </TouchableOpacity>

            {CATEGORIES.map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryCard3D,
                  selectedCategory?.id === category.id && styles.categoryCard3DActive
                ]}
                onPress={() => setSelectedCategory(category)}
              >
                <View style={styles.categoryCardContent}>
                  <View style={styles.categoryCardIconBox}>
                    <MaterialCommunityIcons 
                      name={category.icon} 
                      size={16} 
                      color={selectedCategory?.id === category.id ? COLORS.white : COLORS.primary} 
                    />
                  </View>
                  <Text style={[
                    styles.categoryCardTitle,
                    selectedCategory?.id === category.id && styles.categoryCardTitleActive
                  ]}>{category.name}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {showCategories ? (
        /* Categories Grid */
        <FlatList
          data={CATEGORIES}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.categoriesList}
          numColumns={2}
        />
      ) : (
        /* Products Grid */
        <FlatList
          data={filteredProducts}
          renderItem={renderProductItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.productsList}
          numColumns={2}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <MaterialCommunityIcons name="basket-off" size={50} color={COLORS.gray} />
              <Text style={styles.emptyText}>No products found</Text>
            </View>
          }
        />
      )}
      
      {/* Toast Notification */}
      {toastVisible && (
        <Toast 
          message={toastMessage}
          onHide={() => setToastVisible(false)}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.s,
    paddingVertical: SPACING.s,
    ...SHADOWS.small,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.lightGray,
    marginHorizontal: SPACING.m,
    marginVertical: SPACING.s,
    borderRadius: 25,
    paddingHorizontal: SPACING.m,
    height: 45,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: SIZES.medium,
    color: COLORS.text,
    paddingLeft: SPACING.xs,
  },
  searchIcon: {
    marginRight: SPACING.xs,
  },
  clearButton: {
    padding: SPACING.xs,
  },
  categoryChipsContainer: {
    paddingHorizontal: SPACING.m,
    paddingVertical: SPACING.s,
    flexDirection: 'row',
  },
  categoryChip: {
    marginRight: SPACING.s,
    backgroundColor: COLORS.white,
    borderRadius: 20,
    borderColor: COLORS.primary,
  },
  categoryChipText: {
    fontSize: SIZES.small,
  },
  categoriesList: {
    padding: SPACING.m,
  },
  categoryItem: {
    flex: 1,
    margin: SPACING.s,
    maxWidth: '50%',
  },
  categoryCard: {
    backgroundColor: COLORS.white,
    borderRadius: SPACING.m,
    padding: SPACING.m,
    alignItems: 'center',
    ...SHADOWS.small,
    height: 120,
    justifyContent: 'center',
  },
  categoryIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.s,
  },
  categoryName: {
    color: COLORS.text,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: SPACING.xs,
    fontSize: SIZES.medium,
  },
  productsList: {
    padding: SPACING.m,
  },
  productItem: {
    flex: 1,
    margin: SPACING.xs,
    maxWidth: '48%',
  },
  productCard: {
    backgroundColor: COLORS.white,
    borderRadius: 8,
    overflow: 'hidden',
    ...SHADOWS.small,
  },
  productImage: {
    height: 90,
    width: '100%',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  productContent: {
    padding: SPACING.xs,
  },
  productName: {
    fontSize: SIZES.small,
    fontWeight: 'bold',
    marginBottom: 2,
    color: COLORS.text,
  },
  productPrice: {
    fontSize: SIZES.small,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 4,
  },
  productFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 2,
  },
  farmerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  farmerName: {
    fontSize: SIZES.xSmall,
    color: COLORS.gray,
    marginRight: 4,
  },
  farmerBadge: {
    marginLeft: 2,
  },
  addToCartButton: {
    backgroundColor: COLORS.primary,
    width: 26,
    height: 26,
    borderRadius: 13,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
    marginTop: SPACING.m,
    textAlign: 'center',
  },
  categoriesNewContainer: {
    backgroundColor: '#f8f9fa',
    paddingVertical: SPACING.m,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  categoryCardsContainer: {
    paddingHorizontal: SPACING.m,
    paddingBottom: SPACING.s,
  },
  categoryCard3D: {
    width: 60,
    height: 60,
    marginRight: 6,
    borderRadius: 8,
    backgroundColor: COLORS.white,
    ...SHADOWS.small,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  categoryCard3DActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  categoryCardContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 4,
  },
  categoryCardIconBox: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#f0f2f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 3,
    ...SHADOWS.small,
  },
  categoryCardTitle: {
    fontSize: 9,
    fontWeight: 'bold',
    color: COLORS.text,
    textAlign: 'center',
    marginTop: 2,
  },
  categoryCardTitleActive: {
    color: COLORS.white,
  },
});

export default CategoriesScreen;
