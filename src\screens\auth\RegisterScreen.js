import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Button, TextInput, Checkbox } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import * as Yup from 'yup';

import { COLORS, FONT, SIZES, SPACING } from '../../constants/theme';
import { SCREENS } from '../../constants';
import { PLACEHOLDER_IMAGES } from '../../constants/placeholders';
import useStore from '../../store/useStore';
import { signUp } from '../../lib/supabase';
import SuccessModal from '../../components/SuccessModal';

// Validation schema
const RegisterSchema = Yup.object().shape({
  fullName: Yup.string()
    .min(2, 'Name is too short')
    .required('Full name is required'),
  email: Yup.string()
    .email('Invalid email')
    .required('Email is required'),
  phone: Yup.string()
    .matches(/^[0-9]{10}$/, 'Phone number must be 10 digits')
    .required('Phone number is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Confirm password is required'),
  termsAccepted: Yup.boolean()
    .oneOf([true], 'You must accept the terms and conditions')
});

const RegisterScreen = () => {
  const navigation = useNavigation();
  const { login, setLoading, setError } = useStore();
  const [secureTextEntry, setSecureTextEntry] = useState(true);
  const [secureConfirmTextEntry, setSecureConfirmTextEntry] = useState(true);
  const [isRegistering, setIsRegistering] = useState(false);
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Function to handle registration
  const handleRegister = async (values, { setSubmitting, setFieldError }) => {
    try {
      setIsRegistering(true);
      setLoading(true);

      console.log('Starting registration process...');

      // Register the user with Supabase
      const { data, error } = await signUp(
        values.email,
        values.password,
        {
          fullName: values.fullName,
          phone: values.phone
        }
      );

      console.log('Registration response:', data ? 'Success' : 'Failed', error ? error.message : 'No error');

      if (error) {
        // Handle specific error cases
        if (error.message.includes('email')) {
          setFieldError('email', error.message);
        } else if (error.message.includes('password')) {
          setFieldError('password', error.message);
        } else {
          Alert.alert('Registration Error', error.message);
        }
        return;
      }

      console.log('Showing success modal...');

      // Set success message and show modal
      setSuccessMessage('Your account has been created successfully! Please check your email and verify your account before logging in.');
      setSuccessModalVisible(true);

    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      setError(error.message);
    } finally {
      // Delay state updates to ensure they don't interfere with the Alert
      setTimeout(() => {
        setIsRegistering(false);
        setLoading(false);
        setSubmitting(false);
        console.log('Registration process completed, states reset.');
      }, 1000);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.headerContainer}>
            <Image
              source={{ uri: PLACEHOLDER_IMAGES.LOGO }}
              style={styles.logo}
              resizeMode="contain"
            />
            <Text style={styles.title}>Create Account</Text>
            <Text style={styles.subtitle}>Sign up to get started</Text>
          </View>

          <Formik
            initialValues={{
              fullName: '',
              email: '',
              phone: '',
              password: '',
              confirmPassword: '',
              termsAccepted: false
            }}
            validationSchema={RegisterSchema}
            onSubmit={handleRegister}
          >
            {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
              setFieldValue
            }) => (
              <View style={styles.formContainer}>
                <TextInput
                  label="Full Name"
                  value={values.fullName}
                  onChangeText={handleChange('fullName')}
                  onBlur={handleBlur('fullName')}
                  style={styles.input}
                  mode="outlined"
                  left={<TextInput.Icon icon="account" />}
                />
                {touched.fullName && errors.fullName && (
                  <Text style={styles.errorText}>{errors.fullName}</Text>
                )}

                <TextInput
                  label="Email"
                  value={values.email}
                  onChangeText={handleChange('email')}
                  onBlur={handleBlur('email')}
                  style={styles.input}
                  mode="outlined"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  left={<TextInput.Icon icon="email" />}
                />
                {touched.email && errors.email && (
                  <Text style={styles.errorText}>{errors.email}</Text>
                )}

                <TextInput
                  label="Phone Number"
                  value={values.phone}
                  onChangeText={handleChange('phone')}
                  onBlur={handleBlur('phone')}
                  style={styles.input}
                  mode="outlined"
                  keyboardType="phone-pad"
                  left={<TextInput.Icon icon="phone" />}
                />
                {touched.phone && errors.phone && (
                  <Text style={styles.errorText}>{errors.phone}</Text>
                )}

                <TextInput
                  label="Password"
                  value={values.password}
                  onChangeText={handleChange('password')}
                  onBlur={handleBlur('password')}
                  style={styles.input}
                  mode="outlined"
                  secureTextEntry={secureTextEntry}
                  left={<TextInput.Icon icon="lock" />}
                  right={
                    <TextInput.Icon
                      icon={secureTextEntry ? "eye" : "eye-off"}
                      onPress={() => setSecureTextEntry(!secureTextEntry)}
                    />
                  }
                />
                {touched.password && errors.password && (
                  <Text style={styles.errorText}>{errors.password}</Text>
                )}

                <TextInput
                  label="Confirm Password"
                  value={values.confirmPassword}
                  onChangeText={handleChange('confirmPassword')}
                  onBlur={handleBlur('confirmPassword')}
                  style={styles.input}
                  mode="outlined"
                  secureTextEntry={secureConfirmTextEntry}
                  left={<TextInput.Icon icon="lock-check" />}
                  right={
                    <TextInput.Icon
                      icon={secureConfirmTextEntry ? "eye" : "eye-off"}
                      onPress={() => setSecureConfirmTextEntry(!secureConfirmTextEntry)}
                    />
                  }
                />
                {touched.confirmPassword && errors.confirmPassword && (
                  <Text style={styles.errorText}>{errors.confirmPassword}</Text>
                )}

                <View style={styles.termsContainer}>
                  <Checkbox
                    status={values.termsAccepted ? 'checked' : 'unchecked'}
                    onPress={() => setFieldValue('termsAccepted', !values.termsAccepted)}
                    color={COLORS.primary}
                  />
                  <View style={styles.termsTextContainer}>
                    <Text style={styles.termsText}>
                      I agree to the{' '}
                      <Text style={styles.termsLink}>Terms & Conditions</Text>
                      {' '}and{' '}
                      <Text style={styles.termsLink}>Privacy Policy</Text>
                    </Text>
                  </View>
                </View>
                {touched.termsAccepted && errors.termsAccepted && (
                  <Text style={styles.errorText}>{errors.termsAccepted}</Text>
                )}

                <Button
                  mode="contained"
                  style={styles.button}
                  labelStyle={styles.buttonLabel}
                  onPress={handleSubmit}
                  disabled={isRegistering}
                  loading={isRegistering}
                >
                  {isRegistering ? 'Creating Account...' : 'Sign Up'}
                </Button>
              </View>
            )}
          </Formik>

          <View style={styles.footerContainer}>
            <Text style={styles.footerText}>Already have an account?</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate(SCREENS.LOGIN)}
            >
              <Text style={styles.loginText}>Login</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Success Modal */}
        <SuccessModal
          visible={successModalVisible}
          title="Registration Successful"
          message={successMessage}
          onClose={() => {
            setSuccessModalVisible(false);
            console.log('Success modal closed, navigating to Login screen...');
            navigation.navigate('Login');
          }}
          actionText="Go to Login"
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flexGrow: 1,
    paddingHorizontal: SPACING.l,
    paddingTop: SPACING.xl,
    paddingBottom: SPACING.xxl,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: SPACING.l,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: SPACING.s,
  },
  title: {
    fontSize: SIZES.xxLarge,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
  formContainer: {
    marginBottom: SPACING.l,
  },
  input: {
    marginBottom: SPACING.s,
    backgroundColor: COLORS.white,
  },
  errorText: {
    color: COLORS.error,
    fontSize: SIZES.small,
    marginBottom: SPACING.s,
    marginLeft: SPACING.s,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: SPACING.m,
  },
  termsTextContainer: {
    flex: 1,
    marginLeft: SPACING.xs,
  },
  termsText: {
    fontSize: SIZES.small,
    color: COLORS.gray,
  },
  termsLink: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  button: {
    marginTop: SPACING.m,
    paddingVertical: SPACING.xs,
    borderRadius: 8,
  },
  buttonLabel: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    paddingVertical: SPACING.xs,
  },
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
    marginRight: SPACING.xs,
  },
  loginText: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
});

export default RegisterScreen;
