import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Button, TextInput } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import * as Yup from 'yup';

import { COLORS, FONT, SIZES, SPACING } from '../../constants/theme';
import { SCREENS } from '../../constants';
import { PLACEHOLDER_IMAGES } from '../../constants/placeholders';
import useStore from '../../store/useStore';
import { signIn, supabase } from '../../lib/supabase';
import ErrorModal from '../../components/ErrorModal';

// Validation schema
const LoginSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email')
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
});

const LoginScreen = () => {
  const navigation = useNavigation();
  const { login, setLoading, setError } = useStore();
  const [secureTextEntry, setSecureTextEntry] = useState(true);
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  // Error modal state
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorTitle, setErrorTitle] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [errorAction, setErrorAction] = useState(null);
  const [errorActionText, setErrorActionText] = useState('');
  const [currentEmail, setCurrentEmail] = useState('');

  // Function to handle login
  const handleLogin = async (values, { setSubmitting, setFieldError }) => {
    try {
      setIsLoggingIn(true);
      setLoading(true);

      // Sign in with Supabase
      const { data, error } = await signIn(values.email, values.password);

      if (error) {
        console.log('Login error:', error.message);

        // Store the current email for potential resend action
        setCurrentEmail(values.email);

        // Handle specific error cases
        if (error.message.includes('Email not confirmed')) {
          // Email not verified error - show custom modal
          setErrorTitle('Email Not Verified');
          setErrorMessage('Please verify your email address before logging in. Check your inbox for a verification link.');
          setErrorActionText('Resend Verification');
          setErrorAction(async () => {
            try {
              setIsLoggingIn(true);
              setErrorModalVisible(false);

              const { error: resendError } = await supabase.auth.resend({
                type: 'signup',
                email: values.email,
              });

              if (resendError) throw resendError;

              // Show success message
              Alert.alert(
                'Verification Email Sent',
                'Please check your inbox for the verification link.'
              );
            } catch (err) {
              console.error('Error resending verification:', err);
              Alert.alert('Error', 'Failed to resend verification email. Please try again later.');
            } finally {
              setIsLoggingIn(false);
            }
          });
          setErrorModalVisible(true);
        } else if (error.message.includes('Invalid login credentials')) {
          // Invalid credentials error
          setErrorTitle('Login Failed');
          setErrorMessage('Invalid email or password. Please check your credentials and try again.');
          setErrorAction(null);
          setErrorModalVisible(true);
        } else if (error.message.includes('email')) {
          setFieldError('email', error.message);
        } else if (error.message.includes('password')) {
          setFieldError('password', 'Invalid password');
        } else {
          // Generic error
          setErrorTitle('Login Error');
          setErrorMessage(error.message);
          setErrorAction(null);
          setErrorModalVisible(true);
        }
        return;
      }

      // If successful, check email verification and update the store with user data
      if (data && data.user) {
        // Check if email is verified
        if (!data.user.email_confirmed_at && data.user.email !== '<EMAIL>') {
          // Email not verified
          Alert.alert(
            'Email Verification Required',
            'Please verify your email address before logging in. Check your inbox for a verification link.',
            [
              {
                text: 'Resend Verification',
                onPress: async () => {
                  try {
                    const { error } = await supabase.auth.resend({
                      type: 'signup',
                      email: values.email,
                    });

                    if (error) throw error;

                    Alert.alert(
                      'Verification Email Sent',
                      'Please check your inbox for the verification link.'
                    );
                  } catch (err) {
                    Alert.alert('Error', 'Failed to resend verification email. Please try again later.');
                  }
                },
              },
              {
                text: 'OK',
                style: 'cancel',
              },
            ]
          );
          return;
        }

        // Check if user has admin role in user metadata
        const isAdmin = data.user.user_metadata?.role === 'admin' ||
                       values.email === '<EMAIL>';

        // Log the user in
        login({
          id: data.user.id,
          name: data.user.user_metadata?.full_name || 'User',
          email: data.user.email,
          isAdmin: isAdmin,
          ...data.user.user_metadata
        });

        // Show success message
        Alert.alert(
          'Login Successful',
          `Welcome back, ${data.user.user_metadata?.full_name || 'User'}!`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate to the home screen
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Main' }],
                });
              },
            },
          ],
          { cancelable: false } // Prevent dismissing by tapping outside
        );
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      setError(error.message);
    } finally {
      setIsLoggingIn(false);
      setLoading(false);
      setSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.headerContainer}>
            <Image
              source={{ uri: PLACEHOLDER_IMAGES.LOGO }}
              style={styles.logo}
              resizeMode="contain"
            />
            <Text style={styles.title}>Welcome Back</Text>
            <Text style={styles.subtitle}>Login to your account</Text>
          </View>

          <Formik
            initialValues={{ email: '', password: '' }}
            validationSchema={LoginSchema}
            onSubmit={handleLogin}
          >
            {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
              <View style={styles.formContainer}>
                <TextInput
                  label="Email"
                  value={values.email}
                  onChangeText={handleChange('email')}
                  onBlur={handleBlur('email')}
                  style={styles.input}
                  mode="outlined"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  left={<TextInput.Icon icon="email" />}
                />
                {touched.email && errors.email && (
                  <Text style={styles.errorText}>{errors.email}</Text>
                )}

                <TextInput
                  label="Password"
                  value={values.password}
                  onChangeText={handleChange('password')}
                  onBlur={handleBlur('password')}
                  style={styles.input}
                  mode="outlined"
                  secureTextEntry={secureTextEntry}
                  left={<TextInput.Icon icon="lock" />}
                  right={
                    <TextInput.Icon
                      icon={secureTextEntry ? "eye" : "eye-off"}
                      onPress={() => setSecureTextEntry(!secureTextEntry)}
                    />
                  }
                />
                {touched.password && errors.password && (
                  <Text style={styles.errorText}>{errors.password}</Text>
                )}

                <TouchableOpacity
                  style={styles.forgotPasswordContainer}
                  onPress={() => navigation.navigate(SCREENS.FORGOT_PASSWORD)}
                >
                  <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
                </TouchableOpacity>

                <Button
                  mode="contained"
                  style={styles.button}
                  labelStyle={styles.buttonLabel}
                  onPress={handleSubmit}
                  disabled={isLoggingIn}
                  loading={isLoggingIn}
                >
                  {isLoggingIn ? 'Logging in...' : 'Login'}
                </Button>
              </View>
            )}
          </Formik>

          <View style={styles.footerContainer}>
            <Text style={styles.footerText}>Don't have an account?</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate(SCREENS.REGISTER)}
            >
              <Text style={styles.signupText}>Sign Up</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Error Modal */}
        <ErrorModal
          visible={errorModalVisible}
          title={errorTitle}
          message={errorMessage}
          onClose={() => setErrorModalVisible(false)}
          onAction={errorAction}
          actionText={errorActionText}
          showActionButton={errorAction !== null}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flexGrow: 1,
    paddingHorizontal: SPACING.l,
    paddingTop: SPACING.xl,
    paddingBottom: SPACING.xxl,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: SPACING.m,
  },
  title: {
    fontSize: SIZES.xxLarge,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
  formContainer: {
    marginBottom: SPACING.xl,
  },
  input: {
    marginBottom: SPACING.s,
    backgroundColor: COLORS.white,
  },
  errorText: {
    color: COLORS.error,
    fontSize: SIZES.small,
    marginBottom: SPACING.s,
    marginLeft: SPACING.s,
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginBottom: SPACING.m,
  },
  forgotPasswordText: {
    color: COLORS.primary,
    fontSize: SIZES.medium,
  },
  button: {
    marginTop: SPACING.m,
    paddingVertical: SPACING.xs,
    borderRadius: 8,
  },
  buttonLabel: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    paddingVertical: SPACING.xs,
  },
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
    marginRight: SPACING.xs,
  },
  signupText: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
});

export default LoginScreen;
