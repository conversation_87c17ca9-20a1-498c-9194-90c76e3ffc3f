import { DefaultTheme } from 'react-native-paper';
import { scale } from 'react-native-size-matters';

// Color palette for Zakumunda farm produce marketplace
export const COLORS = {
  primary: '#4CAF50',      // Green - representing freshness and nature
  secondary: '#FF9800',    // Orange - representing fruits and harvest
  tertiary: '#8BC34A',     // Light Green - complementary to primary
  
  white: '#FFFFFF',
  black: '#000000',
  gray: '#6A6A6A',
  lightGray: '#ECECEC',
  
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFC107',
  info: '#2196F3',
  
  background: '#F9F9F9',
  card: '#FFFFFF',
  text: '#333333',
  border: '#E0E0E0',
  notification: '#FF9800',
};

// Typography
export const FONT = {
  regular: 'Roboto-Regular',
  medium: 'Roboto-Medium',
  bold: 'Roboto-Bold',
  light: 'Roboto-Light',
};

// Font sizes
export const SIZES = {
  xSmall: scale(10),
  small: scale(12),
  medium: scale(14),
  large: scale(16),
  xLarge: scale(18),
  xxLarge: scale(20),
  xxxLarge: scale(24),
  title: scale(32),
};

// Spacing
export const SPACING = {
  xs: scale(4),
  s: scale(8),
  m: scale(16),
  l: scale(24),
  xl: scale(32),
  xxl: scale(40),
};

// Border radius
export const RADIUS = {
  small: scale(4),
  medium: scale(8),
  large: scale(12),
  round: scale(50),
};

// Shadow styles
export const SHADOWS = {
  small: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  medium: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    elevation: 4,
  },
  large: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.2,
    shadowRadius: 7,
    elevation: 6,
  },
};

// Paper theme
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: COLORS.primary,
    accent: COLORS.secondary,
    background: COLORS.background,
    surface: COLORS.white,
    text: COLORS.text,
    error: COLORS.error,
    disabled: COLORS.gray,
    placeholder: COLORS.gray,
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: COLORS.notification,
  },
  fonts: {
    regular: {
      fontFamily: FONT.regular,
    },
    medium: {
      fontFamily: FONT.medium,
    },
    light: {
      fontFamily: FONT.light,
    },
    thin: {
      fontFamily: FONT.light,
    },
  },
};
