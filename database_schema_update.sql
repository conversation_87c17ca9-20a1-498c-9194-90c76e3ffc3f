-- Add new fields to the orders table for the offline payment workflow
ALTER TABLE orders ADD COLUMN admin_approval_status TEXT NOT NULL DEFAULT 'pending_approval';
ALTER TABLE orders ADD COLUMN payment_proof_url TEXT;
ALTER TABLE orders ADD COLUMN payment_reference TEXT;
ALTER TABLE orders ADD COLUMN payment_proof_submitted_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN payment_verified_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN payment_verified_by UUID REFERENCES profiles(id);

-- Create a new table for payment methods
CREATE TABLE payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    instructions TEXT,
    account_details JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default payment methods
INSERT INTO payment_methods (name, description, instructions, account_details) VALUES
('Airtel Money', 'Pay using Airtel Money', 'Send payment to our Airtel Money account and upload the transaction receipt.', '{"phone": "**********", "account_name": "Zakumunda Groceries"}'),
('Mpamba', 'Pay using Mpamba', 'Send payment to our Mpamba account and upload the transaction receipt.', '{"phone": "**********", "account_name": "Zakumunda Groceries"}'),
('Bank Transfer', 'Pay via bank transfer', 'Transfer the amount to our bank account and upload the transfer receipt.', '{"bank_name": "Stanbic Bank", "account_number": "**********", "account_name": "Zakumunda Groceries Ltd"}');

-- Create a policy to allow users to update their own orders (for payment proof)
CREATE POLICY "Users can update their own orders payment proof"
ON orders FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Note: Since RLS policies can't use OLD and NEW references, we'll need to
-- implement additional validation in the application code to ensure users
-- can only update payment-related fields.
