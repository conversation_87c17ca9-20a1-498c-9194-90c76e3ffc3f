// Script to identify where phone numbers are stored in the database
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://saxcjcqohvabropicrkk.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNheGNqY3FvaHZhYnJvcGljcmtrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2MTYxNzUsImV4cCI6MjA2MjE5MjE3NX0.ONVChVWSjr4U185BjJgxK8o6JhIxZ2Z46RAL6xxvQVc';
const supabase = createClient(supabaseUrl, supabaseKey);

// Regular expression to identify potential phone number fields
const phoneFieldRegex = /(phone|mobile|cell|contact|tel)/i;

// Regular expression to identify potential phone number values
const phoneValueRegex = /^[\d\s\+\-\(\)]{7,15}$/;

// Function to get all tables in the database
async function getAllTables() {
  try {
    const { data, error } = await supabase.rpc('get_tables');
    
    if (error) {
      console.error('Error getting tables:', error);
      
      // Fallback to a list of common tables
      return ['profiles', 'users', 'orders', 'addresses', 'customers'];
    }
    
    return data;
  } catch (err) {
    console.error('Error in getAllTables:', err);
    
    // Fallback to a list of common tables
    return ['profiles', 'users', 'orders', 'addresses', 'customers'];
  }
}

// Function to get table schema
async function getTableSchema(tableName) {
  try {
    const { data, error } = await supabase.rpc('get_table_schema', { table_name: tableName });
    
    if (error) {
      console.error(`Error getting schema for table ${tableName}:`, error);
      return null;
    }
    
    return data;
  } catch (err) {
    console.error(`Error in getTableSchema for ${tableName}:`, err);
    return null;
  }
}

// Function to check if a column might contain phone numbers based on its name
function isLikelyPhoneColumn(columnName) {
  return phoneFieldRegex.test(columnName);
}

// Function to sample data from a table and column
async function sampleColumnData(tableName, columnName, limit = 5) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select(columnName)
      .limit(limit);
    
    if (error) {
      console.error(`Error sampling data from ${tableName}.${columnName}:`, error);
      return [];
    }
    
    return data;
  } catch (err) {
    console.error(`Error in sampleColumnData for ${tableName}.${columnName}:`, err);
    return [];
  }
}

// Function to check if a value looks like a phone number
function isLikelyPhoneNumber(value) {
  if (!value) return false;
  if (typeof value !== 'string') return false;
  
  // Check if the value matches our phone number pattern
  return phoneValueRegex.test(value);
}

// Function to analyze a specific table
async function analyzeTable(tableName) {
  console.log(`\nAnalyzing table: ${tableName}`);
  
  // Get table schema
  const schema = await getTableSchema(tableName);
  
  if (!schema) {
    console.log(`  Unable to get schema for ${tableName}`);
    
    // Try a direct query to get column names
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (error) {
        console.error(`  Error querying ${tableName}:`, error);
        return;
      }
      
      if (data && data.length > 0) {
        const columns = Object.keys(data[0]);
        console.log(`  Found columns through direct query: ${columns.join(', ')}`);
        
        // Check each column
        for (const column of columns) {
          await analyzeColumn(tableName, column);
        }
      }
    } catch (err) {
      console.error(`  Error in direct query for ${tableName}:`, err);
    }
    
    return;
  }
  
  // Check each column in the schema
  for (const column of schema) {
    await analyzeColumn(tableName, column.column_name);
  }
}

// Function to analyze a specific column
async function analyzeColumn(tableName, columnName) {
  // Check if the column name suggests it might contain phone numbers
  const isLikelyByName = isLikelyPhoneColumn(columnName);
  
  if (isLikelyByName) {
    console.log(`  Column ${columnName} has a name suggesting it might contain phone numbers`);
  } else if (!columnName.includes('id') && !columnName.includes('date')) {
    // For non-obvious columns that aren't IDs or dates, sample some data
    const samples = await sampleColumnData(tableName, columnName);
    
    let phoneNumberCount = 0;
    for (const sample of samples) {
      const value = sample[columnName];
      if (isLikelyPhoneNumber(value)) {
        phoneNumberCount++;
      }
    }
    
    if (phoneNumberCount > 0) {
      console.log(`  Column ${columnName} contains ${phoneNumberCount}/${samples.length} values that look like phone numbers`);
    }
  }
}

// Main function to run the analysis
async function findPhoneNumberColumns() {
  console.log('Starting phone number column analysis...');
  
  // Get all tables
  const tables = await getAllTables();
  console.log(`Found ${tables.length} tables to analyze`);
  
  // Analyze each table
  for (const table of tables) {
    await analyzeTable(table);
  }
  
  // Specific checks for known tables
  console.log('\nChecking specific tables that commonly contain phone numbers:');
  
  // Check profiles table
  try {
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(5);
    
    if (!profilesError && profiles && profiles.length > 0) {
      console.log('\nProfiles table sample:');
      const phoneFields = Object.keys(profiles[0]).filter(field => isLikelyPhoneColumn(field));
      
      if (phoneFields.length > 0) {
        console.log(`  Found potential phone fields: ${phoneFields.join(', ')}`);
        
        // Show sample values
        for (const profile of profiles) {
          console.log('  Profile:');
          for (const field of phoneFields) {
            console.log(`    ${field}: ${profile[field] || 'null'}`);
          }
        }
      } else {
        console.log('  No obvious phone fields found in profiles table');
      }
    }
  } catch (err) {
    console.error('Error checking profiles table:', err);
  }
  
  // Check orders table
  try {
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('*')
      .limit(5);
    
    if (!ordersError && orders && orders.length > 0) {
      console.log('\nOrders table sample:');
      const phoneFields = Object.keys(orders[0]).filter(field => 
        isLikelyPhoneColumn(field) || field === 'delivery_phone' || field === 'phone'
      );
      
      if (phoneFields.length > 0) {
        console.log(`  Found potential phone fields: ${phoneFields.join(', ')}`);
        
        // Show sample values
        for (const order of orders) {
          console.log('  Order:');
          for (const field of phoneFields) {
            console.log(`    ${field}: ${order[field] || 'null'}`);
          }
        }
      } else {
        console.log('  No obvious phone fields found in orders table');
      }
    }
  } catch (err) {
    console.error('Error checking orders table:', err);
  }
  
  console.log('\nAnalysis complete!');
}

// Run the analysis
findPhoneNumberColumns().catch(err => {
  console.error('Error running phone number analysis:', err);
});
