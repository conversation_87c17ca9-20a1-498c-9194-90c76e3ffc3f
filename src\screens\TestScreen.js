import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, Button } from 'react-native';
import { COLORS } from '../constants/theme';
import useStore from '../store/useStore';

const TestScreen = () => {
  const { login } = useStore();

  // Simple login function for testing
  const handleLogin = () => {
    login({
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Test Screen</Text>
        <Text style={styles.subtitle}>If you can see this, the app is working!</Text>
        <Button title="Login as Test User" onPress={handleLogin} />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: COLORS.primary,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
});

export default TestScreen;
