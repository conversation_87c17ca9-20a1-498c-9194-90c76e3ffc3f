import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  Dimensions,
} from 'react-native';
import {
  IconButton,
  Button,
  Divider,
  Card,
  Title,
  Paragraph,
  List,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../../constants/theme';
import { SCREENS } from '../../../constants';
import useStore from '../../../store/useStore';

const { width } = Dimensions.get('window');

const OrderDetailsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { orderId } = route.params || {};
  const { orders } = useStore();

  const order = orders.find(o => o.id === orderId);

  // Order statuses
  const orderStatuses = [
    { id: 'pending', label: 'Order Placed', icon: 'clipboard-check', color: COLORS.warning },
    { id: 'processing', label: 'Processing', icon: 'package-variant', color: COLORS.info },
    { id: 'shipped', label: 'On the Way', icon: 'truck-delivery', color: COLORS.secondary },
    { id: 'delivered', label: 'Delivered', icon: 'check-circle', color: COLORS.success },
  ];

  // Get current status index
  const getCurrentStatusIndex = () => {
    if (order.status === 'cancelled') return -1;
    return orderStatuses.findIndex(status => status.id === order.status);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Sample delivery information (for demo)
  const deliveryInfo = {
    estimatedDelivery: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
    deliveryAgent: 'John Delivery',
    contactNumber: '+1234567890',
  };

  if (!order) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => navigation.goBack()}
          />
          <Text style={styles.headerTitle}>Order Details</Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="clipboard-text-off" size={64} color={COLORS.gray} />
          <Text style={styles.emptyText}>Order not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.white} />

      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => navigation.goBack()}
        />
        <Text style={styles.headerTitle}>Order #{order.id}</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Order Status */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.orderStatusHeader}>
              <View>
                <Text style={styles.sectionTitle}>Order Status</Text>
                <Text style={styles.orderDate}>{formatDate(order.createdAt)}</Text>
              </View>
              {order.status === 'cancelled' ? (
                <View style={[styles.statusBadge, { backgroundColor: COLORS.error }]}>
                  <Text style={styles.statusText}>Cancelled</Text>
                </View>
              ) : (
                <View style={[styles.statusBadge, { backgroundColor: orderStatuses[getCurrentStatusIndex()].color }]}>
                  <Text style={styles.statusText}>{orderStatuses[getCurrentStatusIndex()].label}</Text>
                </View>
              )}
            </View>

            {order.status !== 'cancelled' && (
              <View style={styles.statusTimeline}>
                {orderStatuses.map((status, index) => {
                  const isCompleted = index <= getCurrentStatusIndex();
                  const isActive = index === getCurrentStatusIndex();

                  return (
                    <View key={status.id} style={styles.statusItem}>
                      <View style={styles.statusIconContainer}>
                        <View
                          style={[
                            styles.statusIcon,
                            isCompleted ? { backgroundColor: status.color } : styles.inactiveStatusIcon,
                            isActive && styles.activeStatusIcon,
                          ]}
                        >
                          <MaterialCommunityIcons
                            name={status.icon}
                            size={16}
                            color={isCompleted ? COLORS.white : COLORS.gray}
                          />
                        </View>
                        {index < orderStatuses.length - 1 && (
                          <View
                            style={[
                              styles.statusLine,
                              isCompleted && index < getCurrentStatusIndex()
                                ? { backgroundColor: status.color }
                                : styles.inactiveStatusLine,
                            ]}
                          />
                        )}
                      </View>
                      <View style={styles.statusContent}>
                        <Text
                          style={[
                            styles.statusLabel,
                            isActive && styles.activeStatusLabel,
                          ]}
                        >
                          {status.label}
                        </Text>
                        {isCompleted && (
                          <Text style={styles.statusTimeText}>
                            {index === 0 ? formatDate(order.createdAt) : 'In progress'}
                          </Text>
                        )}
                      </View>
                    </View>
                  );
                })}
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Delivery Tracking */}
        {(order.status === 'shipped' || order.status === 'delivered') && (
          <Card style={styles.card}>
            <Card.Content>
              <Text style={styles.sectionTitle}>Delivery Information</Text>

              <View style={styles.deliveryTrackingContainer}>
                <View style={styles.deliveryIconContainer}>
                  <MaterialCommunityIcons
                    name="truck-delivery"
                    size={48}
                    color={COLORS.primary}
                  />
                </View>

                <View style={styles.deliveryDetailsContainer}>
                  <Text style={styles.deliveryStatus}>
                    {order.status === 'shipped' ? 'Your order is on the way!' : 'Your order has been delivered!'}
                  </Text>

                  <View style={styles.deliveryInfoItem}>
                    <MaterialCommunityIcons name="calendar-clock" size={20} color={COLORS.primary} />
                    <Text style={styles.deliveryInfoText}>
                      {order.status === 'shipped'
                        ? `Estimated delivery: ${deliveryInfo.estimatedDelivery.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}`
                        : `Delivered on: ${new Date(order.updatedAt).toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}`
                      }
                    </Text>
                  </View>

                  <View style={styles.deliveryInfoItem}>
                    <MaterialCommunityIcons name="account" size={20} color={COLORS.primary} />
                    <Text style={styles.deliveryInfoText}>
                      Delivery Agent: {deliveryInfo.deliveryAgent}
                    </Text>
                  </View>

                  <View style={styles.deliveryInfoItem}>
                    <MaterialCommunityIcons name="phone" size={20} color={COLORS.primary} />
                    <Text style={styles.deliveryInfoText}>
                      Contact: {deliveryInfo.contactNumber}
                    </Text>
                  </View>

                  <View style={styles.deliveryInfoItem}>
                    <MaterialCommunityIcons name="map-marker" size={20} color={COLORS.primary} />
                    <Text style={styles.deliveryInfoText}>
                      Delivery Address: {order.customer.address}
                    </Text>
                  </View>
                </View>
              </View>

              {order.status === 'shipped' && (
                <Button
                  mode="outlined"
                  icon="phone"
                  style={styles.contactButton}
                  onPress={() => {
                    // In a real app, this would call the delivery agent
                    alert('This feature would call the delivery agent in a real app');
                  }}
                >
                  Contact Delivery Agent
                </Button>
              )}
            </Card.Content>
          </Card>
        )}

        {/* Order Items */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Order Items</Text>
            {order.items.map((item, index) => (
              <View key={index}>
                <View style={styles.orderItem}>
                  <Image
                    source={{ uri: item.product.image }}
                    style={styles.productImage}
                  />
                  <View style={styles.productInfo}>
                    <Text style={styles.productName}>{item.product.name}</Text>
                    <Text style={styles.productPrice}>
                      ${item.price.toFixed(2)} / {item.product.unit}
                    </Text>
                    <Text style={styles.productQuantity}>
                      Quantity: {item.quantity}
                    </Text>
                  </View>
                  <Text style={styles.itemTotal}>
                    ${(item.price * item.quantity).toFixed(2)}
                  </Text>
                </View>
                {index < order.items.length - 1 && <Divider style={styles.divider} />}
              </View>
            ))}
          </Card.Content>
        </Card>

        {/* Delivery Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Delivery Information</Text>
            <List.Item
              title={order.customer.name}
              description="Customer"
              left={props => <List.Icon {...props} icon="account" />}
            />
            <Divider style={styles.divider} />
            <List.Item
              title={order.customer.phone}
              description="Phone"
              left={props => <List.Icon {...props} icon="phone" />}
            />
            <Divider style={styles.divider} />
            <List.Item
              title={order.customer.address}
              description="Address"
              left={props => <List.Icon {...props} icon="map-marker" />}
            />
          </Card.Content>
        </Card>

        {/* Payment Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Payment Information</Text>
            <List.Item
              title={
                order.paymentMethod === 'card' ? 'Card Payment' :
                order.paymentMethod === 'cash' ? 'Cash on Delivery' : 'Mobile Money'
              }
              description="Payment Method"
              left={props => <List.Icon {...props} icon={
                order.paymentMethod === 'card' ? 'credit-card' :
                order.paymentMethod === 'cash' ? 'cash' : 'cellphone'
              } />}
            />
            <Divider style={styles.divider} />
            <View style={styles.paymentSummary}>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Subtotal</Text>
                <Text style={styles.paymentValue}>${order.total.toFixed(2)}</Text>
              </View>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Delivery Fee</Text>
                <Text style={styles.paymentValue}>$0.00</Text>
              </View>
              <Divider style={styles.divider} />
              <View style={styles.paymentRow}>
                <Text style={[styles.paymentLabel, styles.totalLabel]}>Total</Text>
                <Text style={[styles.paymentValue, styles.totalValue]}>
                  ${order.total.toFixed(2)}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            mode="outlined"
            style={styles.actionButton}
            icon="chat"
            onPress={() => {
              // In a real app, this would open a chat with support
              Alert.alert('Contact Support', 'This feature is not implemented in the demo.');
            }}
          >
            Contact Support
          </Button>
          {order.status !== 'cancelled' && order.status !== 'delivered' && (
            <Button
              mode="outlined"
              style={[styles.actionButton, styles.cancelButton]}
              icon="close"
              onPress={() => {
                // In a real app, this would cancel the order
                Alert.alert('Cancel Order', 'This feature is not implemented in the demo.');
              }}
            >
              Cancel Order
            </Button>
          )}
        </View>

        <View style={styles.bottomSpacer} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.s,
    paddingVertical: SPACING.s,
    ...SHADOWS.small,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  card: {
    margin: SPACING.m,
    ...SHADOWS.small,
  },
  orderStatusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.m,
  },
  sectionTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  orderDate: {
    fontSize: SIZES.small,
    color: COLORS.gray,
  },
  statusBadge: {
    paddingHorizontal: SPACING.s,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: COLORS.white,
    fontSize: SIZES.xSmall,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  statusTimeline: {
    marginTop: SPACING.m,
  },
  statusItem: {
    flexDirection: 'row',
    marginBottom: SPACING.m,
  },
  statusIconContainer: {
    alignItems: 'center',
    marginRight: SPACING.m,
  },
  statusIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
  },
  inactiveStatusIcon: {
    backgroundColor: COLORS.lightGray,
  },
  activeStatusIcon: {
    borderWidth: 2,
    borderColor: COLORS.white,
    ...SHADOWS.small,
  },
  statusLine: {
    width: 2,
    height: 30,
    backgroundColor: COLORS.primary,
  },
  inactiveStatusLine: {
    backgroundColor: COLORS.lightGray,
  },
  statusContent: {
    flex: 1,
  },
  statusLabel: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  activeStatusLabel: {
    color: COLORS.primary,
  },
  statusTimeText: {
    fontSize: SIZES.small,
    color: COLORS.gray,
    marginTop: 2,
  },
  deliveryTrackingContainer: {
    marginVertical: SPACING.m,
  },
  deliveryIconContainer: {
    alignItems: 'center',
    marginBottom: SPACING.m,
  },
  deliveryDetailsContainer: {
    backgroundColor: COLORS.lightGray,
    borderRadius: 8,
    padding: SPACING.m,
  },
  deliveryStatus: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.m,
    textAlign: 'center',
  },
  deliveryInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.s,
  },
  deliveryInfoText: {
    marginLeft: SPACING.s,
    fontSize: SIZES.small,
    color: COLORS.text,
    flex: 1,
  },
  contactButton: {
    marginTop: SPACING.m,
    borderColor: COLORS.primary,
  },
  orderItem: {
    flexDirection: 'row',
    marginVertical: SPACING.s,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 4,
  },
  productInfo: {
    flex: 1,
    marginLeft: SPACING.m,
  },
  productName: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  productPrice: {
    fontSize: SIZES.small,
    color: COLORS.gray,
    marginTop: 2,
  },
  productQuantity: {
    fontSize: SIZES.small,
    marginTop: 2,
  },
  itemTotal: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  divider: {
    marginVertical: SPACING.s,
  },
  paymentSummary: {
    marginTop: SPACING.s,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: SPACING.xs,
  },
  paymentLabel: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
  paymentValue: {
    fontSize: SIZES.medium,
    color: COLORS.text,
  },
  totalLabel: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  totalValue: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: SPACING.m,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: SPACING.xs,
    borderColor: COLORS.primary,
  },
  cancelButton: {
    borderColor: COLORS.error,
    color: COLORS.error,
  },
  bottomSpacer: {
    height: 40,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.m,
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
});

export default OrderDetailsScreen;
