import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import {
  <PERSON>bar,
  Chip,
  Divider,
  Badge,
  Menu,
  Button,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS } from '../../constants';
import useStore from '../../store/useStore';

const AdminOrdersScreen = () => {
  const navigation = useNavigation();
  const { orders, filteredOrders, filterOrders } = useStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);

  // Order statuses
  const orderStatuses = [
    { id: 'pending', label: 'Pending', color: COLORS.warning },
    { id: 'processing', label: 'Processing', color: COLORS.info },
    { id: 'shipped', label: 'Shipped', color: COLORS.secondary },
    { id: 'delivered', label: 'Delivered', color: COLORS.success },
    { id: 'cancelled', label: 'Cancelled', color: COLORS.error },
  ];

  // Handle search
  const onChangeSearch = (query) => {
    setSearchQuery(query);
    // In a real app, you would implement search functionality here
  };

  // Handle status filter
  const handleStatusFilter = (status) => {
    setSelectedStatus(status === selectedStatus ? null : status);
    filterOrders(status === selectedStatus ? null : status);
  };

  // Get status color
  const getStatusColor = (status) => {
    const statusObj = orderStatuses.find(s => s.id === status);
    return statusObj ? statusObj.color : COLORS.gray;
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Render order item
  const renderOrderItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.orderCard}
        onPress={() => navigation.navigate(SCREENS.ADMIN_ORDER_DETAILS, { orderId: item.id })}
      >
        <View style={styles.orderHeader}>
          <View>
            <Text style={styles.orderId}>Order #{item.id}</Text>
            <Text style={styles.orderDate}>{formatDate(item.createdAt)}</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
        </View>

        <Divider style={styles.divider} />

        <View style={styles.customerInfo}>
          <MaterialCommunityIcons name="account" size={20} color={COLORS.gray} />
          <Text style={styles.customerName}>{item.customer.name}</Text>
        </View>

        <View style={styles.orderSummary}>
          <Text style={styles.itemCount}>
            {item.items.reduce((sum, item) => sum + item.quantity, 0)} items
          </Text>
          <Text style={styles.orderTotal}>${item.total.toFixed(2)}</Text>
        </View>

        <View style={styles.paymentMethod}>
          <MaterialCommunityIcons
            name={
              item.paymentMethod === 'card' ? 'credit-card' :
              item.paymentMethod === 'cash' ? 'cash' : 'cellphone'
            }
            size={16}
            color={COLORS.gray}
          />
          <Text style={styles.paymentMethodText}>
            {item.paymentMethod === 'card' ? 'Card Payment' :
             item.paymentMethod === 'cash' ? 'Cash on Delivery' : 'Mobile Money'}
          </Text>
        </View>

        <Button
          mode="outlined"
          style={styles.viewDetailsButton}
          onPress={() => navigation.navigate(SCREENS.ADMIN_ORDER_DETAILS, { orderId: item.id })}
        >
          View Details
        </Button>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Orders</Text>
      </View>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search orders"
          onChangeText={onChangeSearch}
          value={searchQuery}
          style={styles.searchBar}
        />
      </View>

      <View style={styles.filtersContainer}>
        <ScrollingFilters
          filters={orderStatuses}
          selectedFilter={selectedStatus}
          onSelectFilter={handleStatusFilter}
        />
      </View>

      <FlatList
        data={filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons name="clipboard-text" size={64} color={COLORS.gray} />
            <Text style={styles.emptyText}>No orders found</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

// Scrolling Filters Component
const ScrollingFilters = ({ filters, selectedFilter, onSelectFilter }) => {
  return (
    <FlatList
      data={filters}
      horizontal
      showsHorizontalScrollIndicator={false}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <Chip
          selected={selectedFilter === item.id}
          onPress={() => onSelectFilter(item.id)}
          style={[
            styles.filterChip,
            selectedFilter === item.id && { backgroundColor: item.color }
          ]}
          textStyle={[
            styles.filterChipText,
            selectedFilter === item.id && styles.selectedFilterChipText
          ]}
        >
          {item.label}
        </Chip>
      )}
      contentContainerStyle={styles.filtersList}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  searchContainer: {
    padding: SPACING.m,
    backgroundColor: COLORS.white,
    ...SHADOWS.small,
  },
  searchBar: {
    elevation: 0,
    backgroundColor: COLORS.lightGray,
  },
  filtersContainer: {
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.s,
    ...SHADOWS.small,
  },
  filtersList: {
    paddingHorizontal: SPACING.m,
  },
  filterChip: {
    marginRight: SPACING.s,
    backgroundColor: COLORS.lightGray,
  },
  filterChipText: {
    color: COLORS.text,
  },
  selectedFilterChipText: {
    color: COLORS.white,
  },
  listContainer: {
    padding: SPACING.m,
  },
  orderCard: {
    backgroundColor: COLORS.white,
    borderRadius: 8,
    padding: SPACING.m,
    marginBottom: SPACING.m,
    ...SHADOWS.small,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  orderId: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  orderDate: {
    fontSize: SIZES.small,
    color: COLORS.gray,
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: SPACING.s,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: COLORS.white,
    fontSize: SIZES.xSmall,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  divider: {
    marginVertical: SPACING.s,
  },
  customerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.s,
  },
  customerName: {
    marginLeft: SPACING.xs,
    fontSize: SIZES.medium,
    color: COLORS.text,
  },
  orderSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.s,
  },
  itemCount: {
    fontSize: SIZES.small,
    color: COLORS.gray,
  },
  orderTotal: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.m,
  },
  paymentMethodText: {
    marginLeft: SPACING.xs,
    fontSize: SIZES.small,
    color: COLORS.gray,
  },
  viewDetailsButton: {
    borderColor: COLORS.primary,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.m,
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
});

export default AdminOrdersScreen;
