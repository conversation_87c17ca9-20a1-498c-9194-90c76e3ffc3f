import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
} from 'react-native';
import { Card, Title, Paragraph, Button, Divider, Avatar, List } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS } from '../../constants';
import useStore from '../../store/useStore';

const AdminDashboardScreen = () => {
  const navigation = useNavigation();
  const { products, orders, notifications } = useStore();
  const [refreshing, setRefreshing] = useState(false);

  // Calculate dashboard stats
  const totalProducts = products.length;
  const totalOrders = orders.length;
  const pendingOrders = orders.filter(order => order.status === 'pending').length;
  const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
  const unreadNotifications = notifications.filter(notification => !notification.isRead).length;

  // Recent orders (last 5)
  const recentOrders = [...orders]
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 5);

  // Low stock products (less than 10 units)
  const lowStockProducts = products
    .filter(product => product.stock < 10)
    .slice(0, 3);

  // Handle refresh
  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // In a real app, you would fetch fresh data here
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Admin Dashboard</Text>
        <TouchableOpacity
          style={styles.notificationButton}
          onPress={() => navigation.navigate(SCREENS.ADMIN_NOTIFICATIONS)}
        >
          <MaterialCommunityIcons name="bell" size={24} color={COLORS.white} />
          {unreadNotifications > 0 && (
            <View style={styles.notificationBadge}>
              <Text style={styles.notificationBadgeText}>{unreadNotifications}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <Card style={[styles.statsCard, { backgroundColor: COLORS.primary }]}>
            <Card.Content>
              <Title style={styles.statsTitle}>{totalOrders}</Title>
              <Paragraph style={styles.statsSubtitle}>Total Orders</Paragraph>
            </Card.Content>
          </Card>

          <Card style={[styles.statsCard, { backgroundColor: COLORS.secondary }]}>
            <Card.Content>
              <Title style={styles.statsTitle}>{totalProducts}</Title>
              <Paragraph style={styles.statsSubtitle}>Products</Paragraph>
            </Card.Content>
          </Card>

          <Card style={[styles.statsCard, { backgroundColor: COLORS.tertiary }]}>
            <Card.Content>
              <Title style={styles.statsTitle}>${totalRevenue.toFixed(2)}</Title>
              <Paragraph style={styles.statsSubtitle}>Revenue</Paragraph>
            </Card.Content>
          </Card>

          <Card style={[styles.statsCard, { backgroundColor: COLORS.info }]}>
            <Card.Content>
              <Title style={styles.statsTitle}>{pendingOrders}</Title>
              <Paragraph style={styles.statsSubtitle}>Pending Orders</Paragraph>
            </Card.Content>
          </Card>
        </View>

        {/* Quick Actions */}
        <Card style={styles.actionsCard}>
          <Card.Title title="Quick Actions" />
          <Card.Content>
            <View style={styles.actionsContainer}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => navigation.navigate(SCREENS.ADMIN_PRODUCT_FORM)}
              >
                <MaterialCommunityIcons name="plus-circle" size={24} color={COLORS.primary} />
                <Text style={styles.actionButtonText}>Add Product</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => navigation.navigate(SCREENS.ADMIN_ORDERS)}
              >
                <MaterialCommunityIcons name="clipboard-list" size={24} color={COLORS.primary} />
                <Text style={styles.actionButtonText}>View Orders</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => navigation.navigate(SCREENS.ADMIN_PRODUCTS)}
              >
                <MaterialCommunityIcons name="package-variant" size={24} color={COLORS.primary} />
                <Text style={styles.actionButtonText}>Manage Products</Text>
              </TouchableOpacity>
            </View>
          </Card.Content>
        </Card>

        {/* Recent Orders */}
        <Card style={styles.card}>
          <Card.Title title="Recent Orders" />
          <Card.Content>
            {recentOrders.length > 0 ? (
              recentOrders.map((order) => (
                <TouchableOpacity
                  key={order.id}
                  onPress={() => navigation.navigate(SCREENS.ADMIN_ORDER_DETAILS, { orderId: order.id })}
                >
                  <View style={styles.orderItem}>
                    <View style={styles.orderInfo}>
                      <Text style={styles.orderCustomer}>{order.customer.name}</Text>
                      <Text style={styles.orderDate}>
                        {new Date(order.createdAt).toLocaleDateString()}
                      </Text>
                    </View>
                    <View style={styles.orderDetails}>
                      <Text style={styles.orderTotal}>${order.total.toFixed(2)}</Text>
                      <View style={[
                        styles.orderStatus,
                        {
                          backgroundColor:
                            order.status === 'delivered' ? COLORS.success :
                            order.status === 'processing' ? COLORS.info :
                            COLORS.warning
                        }
                      ]}>
                        <Text style={styles.orderStatusText}>{order.status}</Text>
                      </View>
                    </View>
                  </View>
                  <Divider style={styles.divider} />
                </TouchableOpacity>
              ))
            ) : (
              <Text style={styles.emptyText}>No recent orders</Text>
            )}
            {recentOrders.length > 0 && (
              <Button
                mode="text"
                onPress={() => navigation.navigate(SCREENS.ADMIN_ORDERS)}
                style={styles.viewAllButton}
              >
                View All Orders
              </Button>
            )}
          </Card.Content>
        </Card>

        {/* Low Stock Alert */}
        {lowStockProducts.length > 0 && (
          <Card style={styles.card}>
            <Card.Title title="Low Stock Alert" />
            <Card.Content>
              {lowStockProducts.map((product) => (
                <TouchableOpacity
                  key={product.id}
                  onPress={() => navigation.navigate(SCREENS.ADMIN_PRODUCT_FORM, { productId: product.id })}
                >
                  <View style={styles.productItem}>
                    <View style={styles.productInfo}>
                      <Text style={styles.productName}>{product.name}</Text>
                      <Text style={styles.productStock}>
                        Only {product.stock} {product.unit}(s) left
                      </Text>
                    </View>
                    <Button
                      mode="contained"
                      compact
                      style={styles.updateButton}
                      onPress={() => navigation.navigate(SCREENS.ADMIN_PRODUCT_FORM, { productId: product.id })}
                    >
                      Update
                    </Button>
                  </View>
                  <Divider style={styles.divider} />
                </TouchableOpacity>
              ))}
              <Button
                mode="text"
                onPress={() => navigation.navigate(SCREENS.ADMIN_PRODUCTS)}
                style={styles.viewAllButton}
              >
                View All Products
              </Button>
            </Card.Content>
          </Card>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  notificationButton: {
    padding: SPACING.xs,
  },
  notificationBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: COLORS.error,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadgeText: {
    color: COLORS.white,
    fontSize: SIZES.xSmall,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: SPACING.m,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.m,
  },
  statsCard: {
    width: '48%',
    marginBottom: SPACING.m,
    ...SHADOWS.medium,
  },
  statsTitle: {
    color: COLORS.white,
    fontSize: SIZES.xxLarge,
    fontWeight: 'bold',
  },
  statsSubtitle: {
    color: COLORS.white,
    fontSize: SIZES.small,
  },
  card: {
    marginBottom: SPACING.m,
    ...SHADOWS.small,
  },
  actionsCard: {
    marginBottom: SPACING.m,
    ...SHADOWS.small,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  actionButton: {
    alignItems: 'center',
    padding: SPACING.m,
    width: '30%',
  },
  actionButtonText: {
    marginTop: SPACING.xs,
    color: COLORS.text,
    fontSize: SIZES.small,
    textAlign: 'center',
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.s,
  },
  orderInfo: {
    flex: 1,
  },
  orderCustomer: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  orderDate: {
    fontSize: SIZES.small,
    color: COLORS.gray,
  },
  orderDetails: {
    alignItems: 'flex-end',
  },
  orderTotal: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  orderStatus: {
    paddingHorizontal: SPACING.s,
    paddingVertical: SPACING.xs / 2,
    borderRadius: 4,
    marginTop: SPACING.xs,
  },
  orderStatusText: {
    color: COLORS.white,
    fontSize: SIZES.xSmall,
    textTransform: 'capitalize',
  },
  divider: {
    marginVertical: SPACING.s,
  },
  emptyText: {
    textAlign: 'center',
    color: COLORS.gray,
    padding: SPACING.m,
  },
  viewAllButton: {
    marginTop: SPACING.s,
  },
  productItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.s,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  productStock: {
    fontSize: SIZES.small,
    color: COLORS.error,
  },
  updateButton: {
    backgroundColor: COLORS.primary,
  },
});

export default AdminDashboardScreen;
