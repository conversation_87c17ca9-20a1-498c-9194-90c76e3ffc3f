-- Simple SQL script to check table structures

-- Check profiles table structure
SELECT 
    column_name,
    data_type,
    is_nullable
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND table_name = 'profiles'
ORDER BY 
    ordinal_position;

-- Check orders table structure
SELECT 
    column_name,
    data_type,
    is_nullable
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND table_name = 'orders'
ORDER BY 
    ordinal_position;

-- Check addresses table structure
SELECT 
    column_name,
    data_type,
    is_nullable
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND table_name = 'addresses'
ORDER BY 
    ordinal_position;

-- List all tables in the public schema
SELECT 
    table_name
FROM 
    information_schema.tables
WHERE 
    table_schema = 'public'
    AND table_type = 'BASE TABLE'
ORDER BY 
    table_name;

-- Check for any columns with 'phone' in the name across all tables
SELECT 
    table_name,
    column_name,
    data_type
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND column_name ILIKE '%phone%'
ORDER BY 
    table_name, column_name;
