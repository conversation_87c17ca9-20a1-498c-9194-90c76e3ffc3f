import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  Modal,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  IconButton,
  Searchbar,
  Button,
  Chip,
  Divider,
  SegmentedB<PERSON><PERSON>,
  Slider,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS, CATEGORIES } from '../../constants';
import useStore from '../../store/useStore';

const sortOptions = [
  { label: 'Price: Low to High', value: 'price_asc' },
  { label: 'Price: High to Low', value: 'price_desc' },
  { label: 'Rating', value: 'rating' },
  { label: 'Newest', value: 'newest' },
];

const ProductListScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { categoryId, categoryName } = route.params || {};
  const { products, filteredProducts, setSelectedProduct, addToCart, filterProducts } = useStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('newest');
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [priceRange, setPriceRange] = useState([0, 50]);
  const [selectedCategories, setSelectedCategories] = useState(categoryId ? [categoryId] : []);
  const [maxDistance, setMaxDistance] = useState(20);
  
  // Apply initial category filter if provided
  useEffect(() => {
    if (categoryId) {
      filterProducts(categoryId);
    }
  }, [categoryId]);

  // Handle search
  const onChangeSearch = (query) => {
    setSearchQuery(query);
    // In a real app, you would implement search functionality here
  };

  // Handle product press
  const handleProductPress = (product) => {
    setSelectedProduct(product);
    navigation.navigate(SCREENS.PRODUCT_DETAILS, { productId: product.id });
  };

  // Toggle category selection
  const toggleCategorySelection = (categoryId) => {
    if (selectedCategories.includes(categoryId)) {
      setSelectedCategories(selectedCategories.filter(id => id !== categoryId));
    } else {
      setSelectedCategories([...selectedCategories, categoryId]);
    }
  };

  // Apply filters
  const applyFilters = () => {
    // In a real app, you would implement filtering logic here
    setFilterModalVisible(false);
  };

  // Reset filters
  const resetFilters = () => {
    setPriceRange([0, 50]);
    setSelectedCategories(categoryId ? [categoryId] : []);
    setMaxDistance(20);
  };

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price_asc':
        return a.price - b.price;
      case 'price_desc':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      case 'newest':
        return new Date(b.createdAt) - new Date(a.createdAt);
      default:
        return 0;
    }
  });

  // Render product item in grid view
  const renderGridItem = ({ item }) => (
    <TouchableOpacity
      style={styles.gridItem}
      onPress={() => handleProductPress(item)}
    >
      <Card style={styles.gridCard}>
        <Card.Cover source={{ uri: item.image }} style={styles.gridImage} />
        <Card.Content style={styles.gridContent}>
          <Title style={styles.gridTitle} numberOfLines={1}>{item.name}</Title>
          <Paragraph style={styles.gridPrice}>${item.price.toFixed(2)} / {item.unit}</Paragraph>
          <View style={styles.gridFooter}>
            <Text style={styles.farmerName} numberOfLines={1}>{item.farmer.name}</Text>
            <IconButton
              icon="cart-plus"
              size={20}
              color={COLORS.primary}
              style={styles.addToCartButton}
              onPress={() => addToCart(item, 1)}
            />
          </View>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );

  // Render product item in list view
  const renderListItem = ({ item }) => (
    <TouchableOpacity
      style={styles.listItem}
      onPress={() => handleProductPress(item)}
    >
      <Card style={styles.listCard}>
        <View style={styles.listContent}>
          <Image source={{ uri: item.image }} style={styles.listImage} />
          <View style={styles.listDetails}>
            <Title style={styles.listTitle} numberOfLines={1}>{item.name}</Title>
            <Paragraph style={styles.listPrice}>${item.price.toFixed(2)} / {item.unit}</Paragraph>
            <Text style={styles.farmerName} numberOfLines={1}>By {item.farmer.name}</Text>
            <View style={styles.ratingContainer}>
              <MaterialCommunityIcons name="star" size={16} color={COLORS.secondary} />
              <Text style={styles.ratingText}>{item.rating}</Text>
            </View>
          </View>
          <IconButton
            icon="cart-plus"
            size={24}
            color={COLORS.primary}
            style={styles.listAddToCartButton}
            onPress={() => addToCart(item, 1)}
          />
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => navigation.goBack()}
        />
        <Text style={styles.headerTitle}>{categoryName || 'Products'}</Text>
        <IconButton
          icon="magnify"
          size={24}
          onPress={() => navigation.navigate(SCREENS.SEARCH)}
        />
      </View>

      {/* Search and Filter Bar */}
      <View style={styles.searchFilterContainer}>
        <Searchbar
          placeholder="Search products"
          onChangeText={onChangeSearch}
          value={searchQuery}
          style={styles.searchBar}
        />
        <View style={styles.filterSortContainer}>
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setFilterModalVisible(true)}
          >
            <MaterialCommunityIcons name="filter-variant" size={20} color={COLORS.primary} />
            <Text style={styles.filterButtonText}>Filter</Text>
          </TouchableOpacity>
          <View style={styles.viewToggle}>
            <SegmentedButtons
              value={viewMode}
              onValueChange={setViewMode}
              buttons={[
                { value: 'grid', icon: 'grid' },
                { value: 'list', icon: 'format-list-bulleted' },
              ]}
              style={styles.segmentedButtons}
            />
          </View>
        </View>
      </View>

      {/* Sort Options */}
      <View style={styles.sortContainer}>
        <Text style={styles.sortLabel}>Sort by:</Text>
        <FlatList
          data={sortOptions}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item.value}
          renderItem={({ item }) => (
            <Chip
              selected={sortBy === item.value}
              onPress={() => setSortBy(item.value)}
              style={[
                styles.sortChip,
                sortBy === item.value && styles.selectedSortChip
              ]}
              textStyle={[
                styles.sortChipText,
                sortBy === item.value && styles.selectedSortChipText
              ]}
            >
              {item.label}
            </Chip>
          )}
          contentContainerStyle={styles.sortOptionsList}
        />
      </View>

      {/* Products List */}
      <FlatList
        data={sortedProducts}
        renderItem={viewMode === 'grid' ? renderGridItem : renderListItem}
        keyExtractor={(item) => item.id}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode === 'grid' ? 'grid' : 'list'}
        contentContainerStyle={styles.productsList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons name="basket-off" size={64} color={COLORS.gray} />
            <Text style={styles.emptyText}>No products found</Text>
          </View>
        }
      />

      {/* Filter Modal */}
      <Modal
        visible={filterModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setFilterModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Products</Text>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setFilterModalVisible(false)}
              />
            </View>
            
            <Divider />
            
            {/* Price Range */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Price Range</Text>
              <Text style={styles.priceRangeText}>
                ${priceRange[0].toFixed(2)} - ${priceRange[1].toFixed(2)}
              </Text>
              <Slider
                value={priceRange}
                onValueChange={setPriceRange}
                minimumValue={0}
                maximumValue={100}
                step={1}
                range
                style={styles.slider}
                minimumTrackTintColor={COLORS.primary}
                maximumTrackTintColor={COLORS.lightGray}
              />
            </View>
            
            <Divider />
            
            {/* Categories */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Categories</Text>
              <View style={styles.categoriesContainer}>
                {CATEGORIES.map((category) => (
                  <Chip
                    key={category.id}
                    selected={selectedCategories.includes(category.id)}
                    onPress={() => toggleCategorySelection(category.id)}
                    style={[
                      styles.categoryChip,
                      selectedCategories.includes(category.id) && styles.selectedCategoryChip
                    ]}
                    textStyle={[
                      styles.categoryChipText,
                      selectedCategories.includes(category.id) && styles.selectedCategoryChipText
                    ]}
                  >
                    {category.name}
                  </Chip>
                ))}
              </View>
            </View>
            
            <Divider />
            
            {/* Distance */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Maximum Distance</Text>
              <Text style={styles.distanceText}>{maxDistance} km</Text>
              <Slider
                value={maxDistance}
                onValueChange={setMaxDistance}
                minimumValue={1}
                maximumValue={50}
                step={1}
                style={styles.slider}
                minimumTrackTintColor={COLORS.primary}
                maximumTrackTintColor={COLORS.lightGray}
              />
            </View>
            
            <View style={styles.modalFooter}>
              <Button
                mode="outlined"
                onPress={resetFilters}
                style={styles.resetButton}
              >
                Reset
              </Button>
              <Button
                mode="contained"
                onPress={applyFilters}
                style={styles.applyButton}
              >
                Apply Filters
              </Button>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.s,
    paddingVertical: SPACING.s,
    ...SHADOWS.small,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  searchFilterContainer: {
    backgroundColor: COLORS.white,
    padding: SPACING.m,
    ...SHADOWS.small,
  },
  searchBar: {
    elevation: 0,
    backgroundColor: COLORS.lightGray,
    marginBottom: SPACING.s,
  },
  filterSortContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.lightGray,
    paddingHorizontal: SPACING.m,
    paddingVertical: SPACING.xs,
    borderRadius: 20,
  },
  filterButtonText: {
    marginLeft: SPACING.xs,
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  viewToggle: {
    flexDirection: 'row',
  },
  segmentedButtons: {
    backgroundColor: COLORS.lightGray,
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.s,
    paddingHorizontal: SPACING.m,
    ...SHADOWS.small,
  },
  sortLabel: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    marginRight: SPACING.s,
  },
  sortOptionsList: {
    paddingVertical: SPACING.xs,
  },
  sortChip: {
    marginRight: SPACING.s,
    backgroundColor: COLORS.lightGray,
  },
  selectedSortChip: {
    backgroundColor: COLORS.primary,
  },
  sortChipText: {
    color: COLORS.text,
  },
  selectedSortChipText: {
    color: COLORS.white,
  },
  productsList: {
    padding: SPACING.m,
  },
  gridItem: {
    flex: 1,
    margin: SPACING.xs,
    maxWidth: '50%',
  },
  gridCard: {
    ...SHADOWS.small,
  },
  gridImage: {
    height: 120,
  },
  gridContent: {
    padding: SPACING.s,
  },
  gridTitle: {
    fontSize: SIZES.medium,
    marginBottom: 2,
  },
  gridPrice: {
    fontSize: SIZES.small,
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  gridFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 2,
  },
  farmerName: {
    fontSize: SIZES.xSmall,
    color: COLORS.gray,
    flex: 1,
  },
  addToCartButton: {
    margin: 0,
  },
  listItem: {
    marginBottom: SPACING.s,
  },
  listCard: {
    ...SHADOWS.small,
  },
  listContent: {
    flexDirection: 'row',
    padding: SPACING.s,
  },
  listImage: {
    width: 80,
    height: 80,
    borderRadius: 4,
  },
  listDetails: {
    flex: 1,
    marginLeft: SPACING.m,
    justifyContent: 'center',
  },
  listTitle: {
    fontSize: SIZES.medium,
  },
  listPrice: {
    fontSize: SIZES.small,
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  ratingText: {
    marginLeft: 2,
    fontSize: SIZES.xSmall,
    color: COLORS.text,
  },
  listAddToCartButton: {
    alignSelf: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.m,
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: SPACING.xl,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.m,
  },
  modalTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
  },
  filterSection: {
    padding: SPACING.m,
  },
  filterSectionTitle: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    marginBottom: SPACING.s,
  },
  priceRangeText: {
    fontSize: SIZES.medium,
    color: COLORS.primary,
    marginBottom: SPACING.s,
  },
  slider: {
    height: 40,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  categoryChip: {
    margin: SPACING.xs,
    backgroundColor: COLORS.lightGray,
  },
  selectedCategoryChip: {
    backgroundColor: COLORS.primary,
  },
  categoryChipText: {
    color: COLORS.text,
  },
  selectedCategoryChipText: {
    color: COLORS.white,
  },
  distanceText: {
    fontSize: SIZES.medium,
    color: COLORS.primary,
    marginBottom: SPACING.s,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: SPACING.m,
  },
  resetButton: {
    flex: 1,
    marginRight: SPACING.s,
    borderColor: COLORS.primary,
  },
  applyButton: {
    flex: 2,
    backgroundColor: COLORS.primary,
  },
});

export default ProductListScreen;
