import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Image,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
} from 'react-native';
import { IconButton, Button } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import useStore from '../../store/useStore';

const SimpleProductDetails = () => {
  try {
    const navigation = useNavigation();
    const route = useRoute();
    const { productId } = route.params || {};
    console.log('SimpleProductDetails - Received productId:', productId);
    
    const { products = [] } = useStore();
    console.log('SimpleProductDetails - Products count:', products.length);
    
    // Find the product or use a fallback
    const product = products.find(p => p.id === productId);
    console.log('SimpleProductDetails - Found product:', product ? 'Yes' : 'No');
    
    if (!product) {
      return (
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            <IconButton
              icon="arrow-left"
              size={24}
              onPress={() => navigation.goBack()}
            />
            <Text style={styles.headerTitle}>Product Not Found</Text>
            <View style={{width: 40}} />
          </View>
          <View style={styles.errorContainer}>
            <MaterialCommunityIcons name="alert-circle-outline" size={80} color="#888" />
            <Text style={styles.errorText}>
              Sorry, we couldn't find the product you're looking for.
            </Text>
            <Button 
              mode="contained" 
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              Go Back
            </Button>
          </View>
        </SafeAreaView>
      );
    }
    
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#fff" />
        
        {/* Header */}
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => navigation.goBack()}
          />
          <Text style={styles.headerTitle}>Product Details</Text>
          <IconButton
            icon="cart"
            size={24}
            onPress={() => navigation.navigate('CartTab')}
          />
        </View>
        
        <ScrollView>
          {/* Product Image */}
          <Image 
            source={{ uri: product.image }} 
            style={styles.productImage}
            resizeMode="cover"
          />
          
          {/* Product Info */}
          <View style={styles.productInfo}>
            <Text style={styles.productName}>{product.name}</Text>
            <View style={styles.priceRow}>
              <Text style={styles.productPrice}>${product.price.toFixed(2)} / {product.unit}</Text>
              <View style={styles.ratingContainer}>
                {[1,2,3,4,5].map(i => (
                  <MaterialCommunityIcons
                    key={i}
                    name={i <= product.rating ? "star" : "star-outline"}
                    size={16}
                    color={i <= product.rating ? "#F4C430" : "#aaa"}
                  />
                ))}
                <Text style={styles.ratingText}>({product.rating})</Text>
              </View>
            </View>
            
            {/* Description */}
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{product.description}</Text>
            
            {/* Farmer Info */}
            <Text style={styles.sectionTitle}>Farmer</Text>
            <View style={styles.farmerInfo}>
              <View>
                <Text style={styles.farmerName}>{product.farmer.name}</Text>
                <Text style={styles.farmerLocation}>{product.farmer.location}</Text>
              </View>
              <Button 
                mode="outlined" 
                onPress={() => navigation.navigate('FarmerProfile', { farmerId: product.farmer.id })}
              >
                View Profile
              </Button>
            </View>
            
            {/* Add to Cart */}
            <Button
              mode="contained"
              style={styles.addButton}
              onPress={() => {
                navigation.navigate('CartTab');
              }}
            >
              Add to Cart
            </Button>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  } catch (error) {
    console.error('Error in SimpleProductDetails:', error);
    const navigation = useNavigation();
    
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => navigation.goBack()}
          />
          <Text style={styles.headerTitle}>Error</Text>
          <View style={{width: 40}} />
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Something went wrong. Please try again.</Text>
          <Button 
            mode="contained" 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            Go Back
          </Button>
        </View>
      </SafeAreaView>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 20,
    color: '#555',
  },
  backButton: {
    marginTop: 20,
    backgroundColor: '#2E7D32',
  },
  productImage: {
    width: '100%',
    height: 300,
  },
  productInfo: {
    padding: 15,
  },
  productName: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  productPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: 5,
    color: '#555',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  description: {
    fontSize: 14,
    lineHeight: 22,
    color: '#333',
  },
  farmerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    marginVertical: 10,
  },
  farmerName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  farmerLocation: {
    fontSize: 14,
    color: '#555',
  },
  addButton: {
    marginTop: 30,
    backgroundColor: '#2E7D32',
    paddingVertical: 8,
  },
});

export default SimpleProductDetails; 