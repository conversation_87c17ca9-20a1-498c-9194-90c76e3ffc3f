-- PROFILES TABLE
-- Stores user profile information
CREATE TABLE profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    avatar_url TEXT,
    is_admin B<PERSON>OLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a secure RLS policy for the profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own profile
CREATE POLICY "Users can view their own profile"
ON profiles FOR SELECT
USING (auth.uid() = id);

-- Policy: Users can update their own profile
CREATE POLICY "Users can update their own profile"
ON profiles FOR UPDATE
USING (auth.uid() = id);

-- Policy: Admin users can view all profiles
CREATE POLICY "Admin users can view all profiles"
ON profiles FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- CATEGORIES TABLE
-- Stores product categories
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a secure RLS policy for the categories table
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can view categories
CREATE POLICY "Anyone can view categories"
ON categories FOR SELECT
USING (TRUE);

-- Policy: Only admins can insert categories
CREATE POLICY "Only admins can insert categories"
ON categories FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- Policy: Only admins can update categories
CREATE POLICY "Only admins can update categories"
ON categories FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- Policy: Only admins can delete categories
CREATE POLICY "Only admins can delete categories"
ON categories FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- FARMERS TABLE
-- Stores farmer profiles
CREATE TABLE farmers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    business_name TEXT NOT NULL,
    description TEXT,
    location TEXT NOT NULL,
    contact_email TEXT,
    contact_phone TEXT,
    logo_url TEXT,
    banner_url TEXT,
    rating NUMERIC(3,2) DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a secure RLS policy for the farmers table
ALTER TABLE farmers ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can view farmers
CREATE POLICY "Anyone can view farmers"
ON farmers FOR SELECT
USING (TRUE);

-- Policy: Farmers can update their own profile
CREATE POLICY "Farmers can update their own profile"
ON farmers FOR UPDATE
USING (auth.uid() = user_id);

-- Policy: Only admins can insert farmers
CREATE POLICY "Only admins can insert farmers"
ON farmers FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- Policy: Only admins can update all farmers
CREATE POLICY "Only admins can update all farmers"
ON farmers FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- Policy: Only admins can delete farmers
CREATE POLICY "Only admins can delete farmers"
ON farmers FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- PRODUCTS TABLE
-- Stores products for sale
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    price NUMERIC(10,2) NOT NULL,
    unit TEXT NOT NULL,
    stock INTEGER NOT NULL DEFAULT 0,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    farmer_id UUID REFERENCES farmers(id) ON DELETE CASCADE,
    image_url TEXT,
    additional_images JSONB,
    rating NUMERIC(3,2) DEFAULT 0,
    is_organic BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a secure RLS policy for the products table
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can view products
CREATE POLICY "Anyone can view products"
ON products FOR SELECT
USING (TRUE);

-- Policy: Farmers can insert their own products
CREATE POLICY "Farmers can insert their own products"
ON products FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1 FROM farmers
        WHERE id = products.farmer_id AND user_id = auth.uid()
    )
);

-- Policy: Farmers can update their own products
CREATE POLICY "Farmers can update their own products"
ON products FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM farmers
        WHERE id = products.farmer_id AND user_id = auth.uid()
    )
);

-- Policy: Farmers can delete their own products
CREATE POLICY "Farmers can delete their own products"
ON products FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM farmers
        WHERE id = products.farmer_id AND user_id = auth.uid()
    )
);

-- Policy: Only admins can insert any product
CREATE POLICY "Only admins can insert any product"
ON products FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- Policy: Only admins can update any product
CREATE POLICY "Only admins can update any product"
ON products FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- Policy: Only admins can delete any product
CREATE POLICY "Only admins can delete any product"
ON products FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND is_admin = TRUE
    )
);

-- ADDRESSES TABLE
-- Stores user addresses
CREATE TABLE addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    address_line1 TEXT NOT NULL,
    address_line2 TEXT,
    city TEXT NOT NULL,
    state TEXT,
    postal_code TEXT,
    country TEXT NOT NULL DEFAULT 'Uganda',
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a secure RLS policy for the addresses table
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own addresses
CREATE POLICY "Users can view their own addresses"
ON addresses FOR SELECT
USING (auth.uid() = user_id);

-- Policy: Users can insert their own addresses
CREATE POLICY "Users can insert their own addresses"
ON addresses FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own addresses
CREATE POLICY "Users can update their own addresses"
ON addresses FOR UPDATE
USING (auth.uid() = user_id);

-- Policy: Users can delete their own addresses
CREATE POLICY "Users can delete their own addresses"
ON addresses FOR DELETE
USING (auth.uid() = user_id);
