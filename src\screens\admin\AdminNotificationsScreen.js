import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import {
  IconButton,
  Divider,
  Button,
  Menu,
  Portal,
  Dialog,
  Paragraph,
  Checkbox,
  FAB,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { SwipeListView } from 'react-native-swipe-list-view';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS } from '../../constants';
import useStore from '../../store/useStore';

const AdminNotificationsScreen = () => {
  const navigation = useNavigation();
  const { 
    notifications, 
    markNotificationAsRead, 
    markAllNotificationsAsRead, 
    deleteNotification 
  } = useStore();
  
  const [selectedNotifications, setSelectedNotifications] = useState([]);
  const [selectMode, setSelectMode] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmAction, setConfirmAction] = useState('');

  // Format date
  const formatDate = (dateString) => {
    const now = new Date();
    const date = new Date(dateString);
    
    // If today, show time
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      });
    }
    
    // If this year, show month and day
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      });
    }
    
    // Otherwise show full date
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Toggle notification selection
  const toggleNotificationSelection = (notificationId) => {
    if (selectedNotifications.includes(notificationId)) {
      setSelectedNotifications(selectedNotifications.filter(id => id !== notificationId));
    } else {
      setSelectedNotifications([...selectedNotifications, notificationId]);
    }
  };

  // Toggle select mode
  const toggleSelectMode = () => {
    setSelectMode(!selectMode);
    setSelectedNotifications([]);
  };

  // Select all notifications
  const selectAllNotifications = () => {
    if (selectedNotifications.length === notifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(notifications.map(notification => notification.id));
    }
  };

  // Mark selected notifications as read
  const markSelectedAsRead = () => {
    selectedNotifications.forEach(id => {
      markNotificationAsRead(id);
    });
    setSelectedNotifications([]);
    setSelectMode(false);
  };

  // Delete selected notifications
  const deleteSelected = () => {
    setConfirmAction('deleteSelected');
    setConfirmDialogVisible(true);
  };

  // Confirm delete selected
  const confirmDeleteSelected = () => {
    selectedNotifications.forEach(id => {
      deleteNotification(id);
    });
    setSelectedNotifications([]);
    setSelectMode(false);
    setConfirmDialogVisible(false);
  };

  // Mark all as read
  const handleMarkAllAsRead = () => {
    setConfirmAction('markAllAsRead');
    setConfirmDialogVisible(true);
  };

  // Confirm mark all as read
  const confirmMarkAllAsRead = () => {
    markAllNotificationsAsRead();
    setConfirmDialogVisible(false);
  };

  // Render notification item
  const renderNotificationItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        !item.isRead && styles.unreadNotification,
        selectMode && selectedNotifications.includes(item.id) && styles.selectedNotification,
      ]}
      onPress={() => {
        if (selectMode) {
          toggleNotificationSelection(item.id);
        } else if (!item.isRead) {
          markNotificationAsRead(item.id);
        }
      }}
      onLongPress={() => {
        if (!selectMode) {
          setSelectMode(true);
          toggleNotificationSelection(item.id);
        }
      }}
    >
      <View style={styles.notificationContent}>
        {selectMode && (
          <Checkbox
            status={selectedNotifications.includes(item.id) ? 'checked' : 'unchecked'}
            onPress={() => toggleNotificationSelection(item.id)}
            color={COLORS.primary}
            style={styles.checkbox}
          />
        )}
        {!selectMode && !item.isRead && (
          <View style={styles.unreadDot} />
        )}
        <View style={styles.notificationTextContainer}>
          <Text style={styles.notificationTitle}>{item.title}</Text>
          <Text style={styles.notificationMessage}>{item.message}</Text>
          <Text style={styles.notificationTime}>{formatDate(item.createdAt)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  // Render hidden item (swipe actions)
  const renderHiddenItem = ({ item }) => (
    <View style={styles.hiddenItemContainer}>
      <TouchableOpacity
        style={[styles.hiddenButton, styles.markReadButton]}
        onPress={() => markNotificationAsRead(item.id)}
      >
        <MaterialCommunityIcons name="check" size={24} color={COLORS.white} />
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.hiddenButton, styles.deleteButton]}
        onPress={() => {
          setConfirmAction('deleteOne');
          setSelectedNotifications([item.id]);
          setConfirmDialogVisible(true);
        }}
      >
        <MaterialCommunityIcons name="delete" size={24} color={COLORS.white} />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        {selectMode ? (
          <>
            <IconButton
              icon="close"
              size={24}
              color={COLORS.white}
              onPress={toggleSelectMode}
            />
            <Text style={styles.headerTitle}>
              {selectedNotifications.length} selected
            </Text>
            <Menu
              visible={menuVisible}
              onDismiss={() => setMenuVisible(false)}
              anchor={
                <IconButton
                  icon="dots-vertical"
                  size={24}
                  color={COLORS.white}
                  onPress={() => setMenuVisible(true)}
                />
              }
            >
              <Menu.Item
                title="Select All"
                onPress={() => {
                  selectAllNotifications();
                  setMenuVisible(false);
                }}
              />
              <Menu.Item
                title="Mark as Read"
                onPress={() => {
                  markSelectedAsRead();
                  setMenuVisible(false);
                }}
                disabled={selectedNotifications.length === 0}
              />
              <Menu.Item
                title="Delete"
                onPress={() => {
                  deleteSelected();
                  setMenuVisible(false);
                }}
                disabled={selectedNotifications.length === 0}
              />
            </Menu>
          </>
        ) : (
          <>
            <Text style={styles.headerTitle}>Notifications</Text>
            <IconButton
              icon="check-all"
              size={24}
              color={COLORS.white}
              onPress={handleMarkAllAsRead}
              disabled={notifications.filter(n => !n.isRead).length === 0}
            />
            <IconButton
              icon="select"
              size={24}
              color={COLORS.white}
              onPress={toggleSelectMode}
            />
          </>
        )}
      </View>

      {notifications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="bell-off" size={64} color={COLORS.gray} />
          <Text style={styles.emptyText}>No notifications</Text>
        </View>
      ) : (
        <SwipeListView
          data={notifications}
          renderItem={renderNotificationItem}
          renderHiddenItem={renderHiddenItem}
          keyExtractor={(item) => item.id}
          leftOpenValue={75}
          rightOpenValue={-75}
          disableLeftSwipe={selectMode}
          disableRightSwipe={selectMode}
          ItemSeparatorComponent={() => <Divider style={styles.divider} />}
          contentContainerStyle={styles.listContainer}
        />
      )}

      {/* Confirm Dialog */}
      <Portal>
        <Dialog
          visible={confirmDialogVisible}
          onDismiss={() => setConfirmDialogVisible(false)}
        >
          <Dialog.Title>
            {confirmAction === 'deleteSelected'
              ? 'Delete Notifications'
              : confirmAction === 'deleteOne'
              ? 'Delete Notification'
              : 'Mark All as Read'}
          </Dialog.Title>
          <Dialog.Content>
            <Paragraph>
              {confirmAction === 'deleteSelected'
                ? `Are you sure you want to delete ${selectedNotifications.length} notification(s)?`
                : confirmAction === 'deleteOne'
                ? 'Are you sure you want to delete this notification?'
                : 'Are you sure you want to mark all notifications as read?'}
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setConfirmDialogVisible(false)}>Cancel</Button>
            <Button
              onPress={() => {
                if (confirmAction === 'deleteSelected' || confirmAction === 'deleteOne') {
                  confirmDeleteSelected();
                } else {
                  confirmMarkAllAsRead();
                }
              }}
            >
              {confirmAction.includes('delete') ? 'Delete' : 'Mark as Read'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.white,
    flex: 1,
    textAlign: 'center',
  },
  listContainer: {
    flexGrow: 1,
  },
  notificationItem: {
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
  },
  unreadNotification: {
    backgroundColor: COLORS.lightGray,
  },
  selectedNotification: {
    backgroundColor: `${COLORS.primary}20`, // 20% opacity
  },
  notificationContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    marginRight: SPACING.s,
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.primary,
    marginRight: SPACING.m,
  },
  notificationTextContainer: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 2,
  },
  notificationMessage: {
    fontSize: SIZES.small,
    color: COLORS.gray,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: SIZES.xSmall,
    color: COLORS.gray,
  },
  divider: {
    backgroundColor: COLORS.border,
  },
  hiddenItemContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  hiddenButton: {
    width: 75,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  markReadButton: {
    backgroundColor: COLORS.success,
  },
  deleteButton: {
    backgroundColor: COLORS.error,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.m,
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
});

export default AdminNotificationsScreen;
