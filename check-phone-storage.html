<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Phone Number Storage Checker</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .button-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        #output {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #09f;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Phone Number Storage Checker</h1>
        
        <div class="button-container">
            <button id="checkProfilesBtn">Check Profiles Table</button>
            <button id="checkOrdersBtn">Check Orders Table</button>
            <button id="checkAllBtn">Check All Tables</button>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Running query, please wait...</p>
        </div>
        
        <div id="output">Results will appear here...</div>
    </div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://saxcjcqohvabropicrkk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNheGNqY3FvaHZhYnJvcGljcmtrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2MTYxNzUsImV4cCI6MjA2MjE5MjE3NX0.ONVChVWSjr4U185BjJgxK8o6JhIxZ2Z46RAL6xxvQVc';
        const supabase = supabase.createClient(supabaseUrl, supabaseKey);
        
        const outputEl = document.getElementById('output');
        const loadingEl = document.getElementById('loading');
        
        // Helper function to log to the output div
        function log(message) {
            if (typeof message === 'object') {
                message = JSON.stringify(message, null, 2);
            }
            outputEl.textContent += message + '\n';
            // Auto-scroll to bottom
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        // Helper function to clear the output
        function clearOutput() {
            outputEl.textContent = '';
        }
        
        // Show/hide loading indicator
        function setLoading(isLoading) {
            loadingEl.style.display = isLoading ? 'block' : 'none';
        }
        
        // Check profiles table
        async function checkProfilesTable() {
            clearOutput();
            setLoading(true);
            
            try {
                log('--- Checking Profiles Table ---');
                
                // First, check the structure of the profiles table
                const { data: profilesStructure, error: structureError } = await supabase
                    .from('profiles')
                    .select('*')
                    .limit(1);
                
                if (structureError) {
                    log('Error querying profiles table: ' + structureError.message);
                    return;
                }
                
                if (profilesStructure && profilesStructure.length > 0) {
                    const columns = Object.keys(profilesStructure[0]);
                    log('Profiles table columns: ' + columns.join(', '));
                    
                    // Look for phone-related columns
                    const phoneColumns = columns.filter(col => 
                        col.includes('phone') || col.includes('mobile') || col.includes('contact')
                    );
                    
                    if (phoneColumns.length > 0) {
                        log('Potential phone columns in profiles: ' + phoneColumns.join(', '));
                        
                        // Get some sample data
                        const { data: samples, error: samplesError } = await supabase
                            .from('profiles')
                            .select(phoneColumns.join(','))
                            .limit(5);
                        
                        if (samplesError) {
                            log('Error getting samples: ' + samplesError.message);
                        } else {
                            log('Sample phone data from profiles:');
                            samples.forEach((sample, index) => {
                                log(`Sample ${index + 1}: ${JSON.stringify(sample)}`);
                            });
                        }
                    } else {
                        log('No obvious phone columns found in profiles table');
                    }
                }
            } catch (err) {
                log('Error in checkProfilesTable: ' + err.message);
            } finally {
                setLoading(false);
            }
        }
        
        // Check orders table
        async function checkOrdersTable() {
            clearOutput();
            setLoading(true);
            
            try {
                log('--- Checking Orders Table ---');
                
                // First, check the structure of the orders table
                const { data: ordersStructure, error: structureError } = await supabase
                    .from('orders')
                    .select('*')
                    .limit(1);
                
                if (structureError) {
                    log('Error querying orders table: ' + structureError.message);
                    return;
                }
                
                if (ordersStructure && ordersStructure.length > 0) {
                    const columns = Object.keys(ordersStructure[0]);
                    log('Orders table columns: ' + columns.join(', '));
                    
                    // Look for phone-related columns
                    const phoneColumns = columns.filter(col => 
                        col.includes('phone') || col.includes('mobile') || col.includes('contact') || 
                        col === 'delivery_phone'
                    );
                    
                    if (phoneColumns.length > 0) {
                        log('Potential phone columns in orders: ' + phoneColumns.join(', '));
                        
                        // Get some sample data
                        const { data: samples, error: samplesError } = await supabase
                            .from('orders')
                            .select(`id, ${phoneColumns.join(',')}`)
                            .limit(5);
                        
                        if (samplesError) {
                            log('Error getting samples: ' + samplesError.message);
                        } else {
                            log('Sample phone data from orders:');
                            samples.forEach((sample, index) => {
                                log(`Sample ${index + 1}: ${JSON.stringify(sample)}`);
                            });
                        }
                    } else {
                        log('No obvious phone columns found in orders table');
                    }
                }
                
                // Check orders with profiles join
                log('\n--- Checking Orders with Profiles Join ---');
                const { data, error } = await supabase
                    .from('orders')
                    .select(`
                        id,
                        user_id,
                        profiles!user_id (
                            id, full_name, email, phone, mobile, contact_number
                        )
                    `)
                    .limit(3);
                
                if (error) {
                    log('Error joining orders with profiles: ' + error.message);
                } else {
                    log('Sample orders with profile data:');
                    data.forEach((item, index) => {
                        log(`Order ${index + 1}: ${JSON.stringify({
                            id: item.id,
                            user_id: item.user_id,
                            profile: item.profiles
                        })}`);
                    });
                }
                
            } catch (err) {
                log('Error in checkOrdersTable: ' + err.message);
            } finally {
                setLoading(false);
            }
        }
        
        // Check all tables
        async function checkAllTables() {
            clearOutput();
            setLoading(true);
            
            try {
                await checkProfilesTable();
                log('\n');
                await checkOrdersTable();
                
                // Also check addresses table
                log('\n--- Checking Addresses Table ---');
                try {
                    // First, check the structure of the addresses table
                    const { data: addressesStructure, error: structureError } = await supabase
                        .from('addresses')
                        .select('*')
                        .limit(1);
                    
                    if (structureError) {
                        log('Error querying addresses table: ' + structureError.message);
                    } else if (addressesStructure && addressesStructure.length > 0) {
                        const columns = Object.keys(addressesStructure[0]);
                        log('Addresses table columns: ' + columns.join(', '));
                        
                        // Look for phone-related columns
                        const phoneColumns = columns.filter(col => 
                            col.includes('phone') || col.includes('mobile') || col.includes('contact')
                        );
                        
                        if (phoneColumns.length > 0) {
                            log('Potential phone columns in addresses: ' + phoneColumns.join(', '));
                            
                            // Get some sample data
                            const { data: samples, error: samplesError } = await supabase
                                .from('addresses')
                                .select(`id, ${phoneColumns.join(',')}`)
                                .limit(5);
                            
                            if (samplesError) {
                                log('Error getting samples: ' + samplesError.message);
                            } else {
                                log('Sample phone data from addresses:');
                                samples.forEach((sample, index) => {
                                    log(`Sample ${index + 1}: ${JSON.stringify(sample)}`);
                                });
                            }
                        } else {
                            log('No obvious phone columns found in addresses table');
                        }
                    }
                } catch (err) {
                    log('Error checking addresses table: ' + err.message);
                }
                
            } catch (err) {
                log('Error in checkAllTables: ' + err.message);
            } finally {
                setLoading(false);
            }
        }
        
        // Set up event listeners
        document.getElementById('checkProfilesBtn').addEventListener('click', checkProfilesTable);
        document.getElementById('checkOrdersBtn').addEventListener('click', checkOrdersTable);
        document.getElementById('checkAllBtn').addEventListener('click', checkAllTables);
    </script>
</body>
</html>
