-- Check profiles table data

-- Get a sample of profiles data
SELECT 
    *
FROM 
    profiles
LIMIT 5;

-- Check if there's a phone column in profiles
SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'phone'
) as has_phone_column;

-- Check if there's a contact_number column in profiles
SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'contact_number'
) as has_contact_number_column;

-- Check for any text columns in profiles that might contain phone numbers
SELECT 
    column_name
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND table_name = 'profiles'
    AND data_type IN ('character varying', 'text', 'character')
ORDER BY 
    column_name;

-- Check for non-null values in the phone column (if it exists)
SELECT 
    COUNT(*) as total_profiles,
    SUM(CASE WHEN phone IS NOT NULL THEN 1 ELSE 0 END) as profiles_with_phone
FROM 
    profiles
WHERE 
    EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'phone'
    );

-- Check for a specific profile with a phone number
SELECT 
    id,
    full_name,
    email,
    phone
FROM 
    profiles
WHERE 
    EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'phone'
    )
    AND phone IS NOT NULL
LIMIT 1;
