import React, { useState, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  Dimensions,
  Modal,
} from 'react-native';
import {
  IconButton,
  Button,
  Divider,
  Card,
  Title,
  Paragraph,
  Badge,
  List,
  Avatar,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import <PERSON>Viewer from 'react-native-image-zoom-viewer';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS } from '../../constants';
import useStore from '../../store/useStore';
import Toast from '../../components/Toast';

const { width: screenWidth } = Dimensions.get('window');

// Sample reviews
const sampleReviews = [
  {
    id: '1',
    user: {
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    },
    rating: 5,
    comment: 'Very fresh and tasty! Will buy again.',
    date: '2023-04-15',
  },
  {
    id: '2',
    user: {
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
    },
    rating: 4,
    comment: 'Good quality but a bit expensive.',
    date: '2023-04-10',
  },
  {
    id: '3',
    user: {
      name: 'Robert Johnson',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    },
    rating: 5,
    comment: 'Excellent product, very fresh!',
    date: '2023-04-05',
  },
];

const ProductDetailsScreen = () => {
  try {
    const navigation = useNavigation();
    const route = useRoute();
    const { productId } = route.params || {};
    const { products = [], addToCart, cart = [] } = useStore();
    
    console.log('ProductDetailsScreen - Received productId:', productId);
    console.log('ProductDetailsScreen - Available products count:', products?.length || 0);
    
    const product = productId && products ? products.find(p => p.id === productId) : null;
    console.log('ProductDetailsScreen - Found product:', product ? 'Yes' : 'No');
    
    // Fallback product for testing if the product is not found
    const fallbackProduct = {
      id: 'fallback',
      name: 'Sample Product',
      description: 'This is a fallback product shown when the requested product cannot be found.',
      price: 0.00,
      unit: 'item',
      rating: 0,
      image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e',
      farmer: {
        id: 'fallback',
        name: 'Sample Farmer',
        location: 'Unknown Location',
        rating: 0,
      }
    };

    const [quantity, setQuantity] = useState(1);
    const [galleryVisible, setGalleryVisible] = useState(false);
    const [expandDescription, setExpandDescription] = useState(false);
    const [toastVisible, setToastVisible] = useState(false);
    const [toastMessage, setToastMessage] = useState('');

    // Generate additional images for the image viewer
    const productImages = [
      { url: (product || fallbackProduct).image || 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e' },
      { url: 'https://images.unsplash.com/photo-1566842600175-97dca3c5bd01' },
      { url: 'https://images.unsplash.com/photo-1596289924644-5308307a66f9' },
    ];

    // Handle quantity change
    const handleQuantityChange = (value) => {
      const newQuantity = Math.max(1, quantity + value);
      setQuantity(newQuantity);
    };

    // Handle add to cart
    const handleAddToCart = () => {
      // Use displayProduct instead of product to handle fallback case
      addToCart(displayProduct, quantity);
      
      // Show in-app feedback instead of an alert
      setToastMessage(`${displayProduct.name} added to cart!`);
      setToastVisible(true);
      
      // Optionally: Navigate to cart
      // navigation.navigate('CartTab');
    };

    // Render rating stars
    const renderRatingStars = (rating = 0) => {
      const stars = [];
      for (let i = 1; i <= 5; i++) {
        stars.push(
          <MaterialCommunityIcons
            key={i}
            name={i <= rating ? 'star' : 'star-outline'}
            size={16}
            color={i <= rating ? COLORS.secondary : COLORS.gray}
            style={{ marginRight: 2 }}
          />
        );
      }
      return stars;
    };

    // Use either the found product or the fallback
    const displayProduct = product || fallbackProduct;

    if (!displayProduct) {
      // This should never happen now with fallback, but keep as extra safety
      return (
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            <IconButton
              icon="arrow-left"
              size={24}
              onPress={() => navigation.goBack()}
            />
            <Text style={styles.headerTitle}>Product Details</Text>
            <IconButton
              icon="cart"
              size={24}
              onPress={() => navigation.navigate('CartTab')}
            />
          </View>
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons name="alert-circle" size={64} color={COLORS.gray} />
            <Text style={styles.emptyText}>Product not found</Text>
          </View>
        </SafeAreaView>
      );
    }

    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={COLORS.white} />
        
        {/* Header */}
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => navigation.goBack()}
          />
          <Text style={styles.headerTitle}>Product Details</Text>
          <TouchableOpacity
            style={styles.cartButton}
            onPress={() => navigation.navigate('CartTab')}
          >
            <MaterialCommunityIcons name="cart" size={24} color={COLORS.text} />
            {cart.length > 0 && (
              <Badge style={styles.cartBadge}>{cart.length}</Badge>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Product Image (Simple version replacing Carousel) */}
          <TouchableOpacity
            style={styles.imageContainer}
            onPress={() => setGalleryVisible(true)}
          >
            <Image 
              source={{ uri: productImages[0].url }} 
              style={styles.productImage} 
              resizeMode="cover"
            />
            <View style={styles.imageBadge}>
              <MaterialCommunityIcons name="image-multiple" size={20} color="#fff" />
              <Text style={styles.imageBadgeText}>{productImages.length}</Text>
            </View>
          </TouchableOpacity>

          {/* Product Info */}
          <View style={styles.productInfoContainer}>
            <Text style={styles.productName}>{displayProduct.name}</Text>
            <View style={styles.priceRatingContainer}>
              <Text style={styles.productPrice}>${displayProduct.price.toFixed(2)} / {displayProduct.unit}</Text>
              <View style={styles.ratingContainer}>
                {renderRatingStars(displayProduct.rating)}
                <Text style={styles.ratingText}>({displayProduct.rating})</Text>
              </View>
            </View>

            {/* Quantity Selector */}
            <View style={styles.quantityContainer}>
              <Text style={styles.quantityLabel}>Quantity:</Text>
              <View style={styles.quantitySelector}>
                <IconButton
                  icon="minus"
                  size={20}
                  color={COLORS.text}
                  style={styles.quantityButton}
                  onPress={() => handleQuantityChange(-1)}
                  disabled={quantity <= 1}
                />
                <Text style={styles.quantityText}>{quantity}</Text>
                <IconButton
                  icon="plus"
                  size={20}
                  color={COLORS.text}
                  style={styles.quantityButton}
                  onPress={() => handleQuantityChange(1)}
                />
              </View>
            </View>

            {/* Add to Cart Button */}
            <Button
              mode="contained"
              style={styles.addToCartButton}
              labelStyle={styles.addToCartButtonLabel}
              onPress={handleAddToCart}
            >
              Add to Cart - ${(displayProduct.price * quantity).toFixed(2)}
            </Button>
          </View>

          <Divider style={styles.divider} />

          {/* Description */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text
              style={styles.descriptionText}
              numberOfLines={expandDescription ? undefined : 3}
            >
              {displayProduct.description}
            </Text>
            {displayProduct.description.length > 100 && (
              <TouchableOpacity
                style={styles.readMoreButton}
                onPress={() => setExpandDescription(!expandDescription)}
              >
                <Text style={styles.readMoreText}>
                  {expandDescription ? 'Read Less' : 'Read More'}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          <Divider style={styles.divider} />

          {/* Farmer Info */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Farmer Information</Text>
            <Card style={styles.farmerCard}>
              <Card.Content style={styles.farmerCardContent}>
                <Avatar.Image
                  size={60}
                  source={{ uri: 'https://randomuser.me/api/portraits/men/32.jpg' }}
                />
                <View style={styles.farmerInfo}>
                  <Title style={styles.farmerName}>{displayProduct.farmer.name}</Title>
                  <Paragraph style={styles.farmerLocation}>{displayProduct.farmer.location}</Paragraph>
                  <View style={styles.farmerRatingContainer}>
                    {renderRatingStars(displayProduct.farmer.rating)}
                    <Text style={styles.farmerRatingText}>({displayProduct.farmer.rating})</Text>
                  </View>
                </View>
                <Button
                  mode="outlined"
                  style={styles.viewProfileButton}
                  onPress={() => navigation.navigate(SCREENS.FARMER_PROFILE, { farmerId: displayProduct.farmer.id })}
                >
                  View Profile
                </Button>
              </Card.Content>
            </Card>
          </View>

          <Divider style={styles.divider} />

          {/* Reviews */}
          <View style={styles.sectionContainer}>
            <View style={styles.reviewsHeader}>
              <Text style={styles.sectionTitle}>Reviews</Text>
              <TouchableOpacity>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>
            
            {sampleReviews.map((review) => (
              <View key={review.id} style={styles.reviewItem}>
                <View style={styles.reviewHeader}>
                  <View style={styles.reviewUser}>
                    <Avatar.Image
                      size={40}
                      source={{ uri: review.user.avatar }}
                    />
                    <View style={styles.reviewUserInfo}>
                      <Text style={styles.reviewUserName}>{review.user.name}</Text>
                      <Text style={styles.reviewDate}>{new Date(review.date).toLocaleDateString()}</Text>
                    </View>
                  </View>
                  <View style={styles.reviewRating}>
                    {renderRatingStars(review.rating)}
                  </View>
                </View>
                <Text style={styles.reviewComment}>{review.comment}</Text>
              </View>
            ))}
          </View>

          {/* Recommended Products */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>You May Also Like</Text>
            {/* Recommended products would go here */}
          </View>

          <View style={styles.bottomSpacer} />
        </ScrollView>

        {/* Toast Notification */}
        {toastVisible && (
          <Toast 
            message={toastMessage} 
            onHide={() => setToastVisible(false)} 
          />
        )}

        {/* Image Gallery Modal */}
        <Modal
          visible={galleryVisible}
          transparent={true}
          onRequestClose={() => setGalleryVisible(false)}
        >
          <ImageViewer
            imageUrls={productImages}
            enableSwipeDown
            onSwipeDown={() => setGalleryVisible(false)}
            renderHeader={() => (
              <IconButton
                icon="close"
                color={COLORS.white}
                size={24}
                style={styles.closeGalleryButton}
                onPress={() => setGalleryVisible(false)}
              />
            )}
          />
        </Modal>
      </SafeAreaView>
    );
  } catch (error) {
    console.error('Error in ProductDetailsScreen:', error);
    
    // Simplified fallback UI in case of errors
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#fff' }}>
        <View style={{ 
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: 16,
          borderBottomWidth: 1,
          borderBottomColor: '#eee'
        }}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => useNavigation().goBack()}
          />
          <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Product Details</Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
          <MaterialCommunityIcons name="alert-circle" size={64} color="#888" />
          <Text style={{ marginTop: 20, fontSize: 16, textAlign: 'center' }}>
            There was an error loading the product details. Please try again.
          </Text>
        </View>
      </SafeAreaView>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.s,
    paddingVertical: SPACING.s,
    ...SHADOWS.small,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  cartButton: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: COLORS.error,
  },
  imageContainer: {
    position: 'relative',
    backgroundColor: COLORS.white,
    height: 300,
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  imageBadge: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 15,
  },
  imageBadgeText: {
    color: '#fff',
    marginLeft: 5,
    fontSize: 12,
  },
  productInfoContainer: {
    backgroundColor: COLORS.white,
    padding: SPACING.m,
  },
  productName: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.s,
  },
  priceRatingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.m,
  },
  productPrice: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: SPACING.xs,
    fontSize: SIZES.small,
    color: COLORS.text,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.m,
  },
  quantityLabel: {
    fontSize: SIZES.medium,
    color: COLORS.text,
    marginRight: SPACING.m,
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 4,
  },
  quantityButton: {
    margin: 0,
  },
  quantityText: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    paddingHorizontal: SPACING.s,
  },
  addToCartButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
  },
  addToCartButtonLabel: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    paddingVertical: SPACING.xs,
  },
  divider: {
    height: 8,
    backgroundColor: COLORS.lightGray,
  },
  sectionContainer: {
    backgroundColor: COLORS.white,
    padding: SPACING.m,
  },
  sectionTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.s,
  },
  descriptionText: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
    lineHeight: 22,
  },
  readMoreButton: {
    marginTop: SPACING.xs,
  },
  readMoreText: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  farmerCard: {
    elevation: 0,
    backgroundColor: COLORS.lightGray,
  },
  farmerCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  farmerInfo: {
    flex: 1,
    marginLeft: SPACING.m,
  },
  farmerName: {
    fontSize: SIZES.medium,
    marginBottom: 0,
  },
  farmerLocation: {
    fontSize: SIZES.small,
    color: COLORS.gray,
  },
  farmerRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.xs,
  },
  farmerRatingText: {
    marginLeft: SPACING.xs,
    fontSize: SIZES.small,
    color: COLORS.text,
  },
  viewProfileButton: {
    borderColor: COLORS.primary,
  },
  reviewsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.m,
  },
  seeAllText: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  reviewItem: {
    marginBottom: SPACING.m,
    padding: SPACING.s,
    backgroundColor: COLORS.lightGray,
    borderRadius: 8,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.s,
  },
  reviewUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reviewUserInfo: {
    marginLeft: SPACING.s,
  },
  reviewUserName: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
  },
  reviewDate: {
    fontSize: SIZES.xSmall,
    color: COLORS.gray,
  },
  reviewRating: {
    flexDirection: 'row',
  },
  reviewComment: {
    fontSize: SIZES.small,
    color: COLORS.text,
    lineHeight: 20,
  },
  bottomSpacer: {
    height: 100,
  },
  closeGalleryButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 1000,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.m,
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
});

export default ProductDetailsScreen;
