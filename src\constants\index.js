// App constants
export const APP_NAME = 'Zakumunda';
export const APP_DESCRIPTION = 'Farm Produce Marketplace';

// API endpoints (to be replaced with actual endpoints)
export const API = {
  BASE_URL: 'https://api.zakumunda.com',
  PRODUCTS: '/products',
  CATEGORIES: '/categories',
  FARMERS: '/farmers',
  ORDERS: '/orders',
  USERS: '/users',
  AUTH: '/auth',
};

// Product categories
export const CATEGORIES = [
  {
    id: '1',
    name: 'Vegetables',
    icon: 'leaf',
    description: 'Fresh locally grown vegetables',
    image: 'https://images.unsplash.com/photo-1566385101042-1a0aa0c1268c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80',
  },
  {
    id: '2',
    name: 'Fruits',
    icon: 'apple',
    description: 'Sweet and juicy fruits',
    image: 'https://images.unsplash.com/photo-1619566636858-adf3ef46400b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
  },
  {
    id: '3',
    name: 'Grains',
    icon: 'grain',
    description: 'Nutritious grains and cereals',
    image: 'https://images.unsplash.com/photo-1574323347407-f5e1c5a1ec21?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
  },
  {
    id: '4',
    name: 'Dairy',
    icon: 'water',
    description: 'Fresh milk and dairy products',
    image: 'https://images.unsplash.com/photo-1628088062854-d1870b4553da?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
  },
  {
    id: '5',
    name: 'Meat',
    icon: 'food-steak',
    description: 'Quality meat products',
    image: 'https://images.unsplash.com/photo-1607623814075-e51df1bdc82f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
  },
  {
    id: '6',
    name: 'Herbs',
    icon: 'grass',
    description: 'Fresh aromatic herbs',
    image: 'https://images.unsplash.com/photo-1600508537197-e58ced11e0a4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
  },
];

// Sample products (for development)
export const SAMPLE_PRODUCTS = [
  {
    id: '1',
    name: 'Fresh Tomatoes',
    description: 'Locally grown fresh tomatoes from sustainable farms. Rich in flavor and perfect for salads, sauces, and cooking. Our tomatoes are harvested at peak ripeness to ensure the best taste and nutritional value. No pesticides or harmful chemicals used in the growing process.',
    price: 2.99,
    unit: 'kg',
    category: '1',
    rating: 4.7,
    farmer: {
      id: '1',
      name: 'John Farmer',
      location: 'Kampala East',
      rating: 4.8,
    },
    image: 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    stock: 50,
  },
  {
    id: '2',
    name: 'Organic Bananas',
    description: 'Sweet organic bananas grown naturally without chemicals. These delicious fruits are perfect for snacking, baking, or adding to your morning smoothie. Harvested at the perfect ripeness to ensure optimal sweetness and nutritional benefits. Rich in potassium and other essential nutrients.',
    price: 1.99,
    unit: 'bunch',
    category: '2',
    rating: 4.5,
    farmer: {
      id: '2',
      name: 'Mary Gardens',
      location: 'Entebbe',
      rating: 4.6,
    },
    image: 'https://images.unsplash.com/photo-1603833665858-e61d17a86224?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80',
    stock: 30,
  },
  {
    id: '3',
    name: 'Fresh Maize',
    description: 'Sweet corn harvested daily from local farms. Our maize is picked at the perfect time to ensure maximum sweetness and tenderness. Great for grilling, boiling, or adding to salads and other dishes. Contains essential vitamins and minerals for a healthy diet. Naturally grown without artificial pesticides.',
    price: 0.99,
    unit: 'piece',
    category: '3',
    rating: 4.8,
    farmer: {
      id: '1',
      name: 'John Farmer',
      location: 'Kampala East',
      rating: 4.8,
    },
    image: 'https://images.unsplash.com/photo-**********-cd27e38d2076?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    stock: 100,
  },
];

// Screen names
export const SCREENS = {
  // Customer screens
  HOME: 'Home',
  PRODUCT_DETAILS: 'ProductDetails',
  CATEGORY: 'Category',
  CART: 'Cart',
  CHECKOUT: 'Checkout',
  PROFILE: 'Profile',
  ORDERS: 'Orders',
  ORDER_DETAILS: 'OrderDetails',
  LOGIN: 'Login',
  REGISTER: 'Register',
  FORGOT_PASSWORD: 'ForgotPassword',
  WELCOME: 'Welcome',
  SETTINGS: 'Settings',
  FARMER_PROFILE: 'FarmerProfile',
  SEARCH: 'Search',
  NOTIFICATIONS: 'Notifications',

  // Admin screens
  ADMIN_DASHBOARD: 'AdminDashboard',
  ADMIN_PRODUCTS: 'AdminProducts',
  ADMIN_PRODUCT_FORM: 'AdminProductForm',
  ADMIN_ORDERS: 'AdminOrders',
  ADMIN_ORDER_DETAILS: 'AdminOrderDetails',
  ADMIN_NOTIFICATIONS: 'AdminNotifications',
};
