-- Add delivery information columns to the orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_phone TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_address TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_city TEXT;

-- Add payment information columns to the orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_proof_url TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_reference TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_proof_submitted_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_verified_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_verified_by UUID REFERENCES auth.users(id);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_amount NUMERIC(10,2);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_currency TEXT DEFAULT 'ZMW';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_method_details JSONB;

-- Disable RLS temporarily to allow direct updates for testing
-- Note: This is a temporary measure and should be re-enabled in production
ALTER TABLE orders DISABLE ROW LEVEL SECURITY;

-- Create a function to update existing orders with delivery information from addresses
CREATE OR REPLACE FUNCTION update_orders_with_delivery_info()
RETURNS void AS $$
DECLARE
  order_record RECORD;
  address_record RECORD;
BEGIN
  FOR order_record IN
    SELECT o.id, o.address_id, o.user_id
    FROM orders o
    WHERE o.delivery_address IS NULL AND o.address_id IS NOT NULL
  LOOP
    -- Get the address information
    SELECT * INTO address_record
    FROM addresses
    WHERE id = order_record.address_id;

    IF FOUND THEN
      -- Update the order with delivery information
      UPDATE orders
      SET
        delivery_address = address_record.address_line1,
        delivery_city = address_record.city,
        delivery_phone = address_record.phone
      WHERE id = order_record.id;

      RAISE NOTICE 'Updated order % with delivery information from address %',
        order_record.id, order_record.address_id;
    ELSE
      RAISE NOTICE 'Could not find address % for order %',
        order_record.address_id, order_record.id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;
