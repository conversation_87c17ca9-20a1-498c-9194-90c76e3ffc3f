import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  ImageBackground
} from 'react-native';
import { Button } from 'react-native-paper';
import { scale } from 'react-native-size-matters';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING } from '../constants/theme';
import { SCREENS } from '../constants';
import { PLACEHOLDER_IMAGES } from '../constants/placeholders';
import useStore from '../store/useStore';

const WelcomeScreen = () => {
  const navigation = useNavigation();
  const { login } = useStore();

  // Function to handle guest login
  const handleGuestMode = () => {
    // Set a guest user in the store
    login({
      id: 'guest',
      name: 'Guest User',
      email: '<EMAIL>',
      isGuest: true,
    });
  };

  return (
    <ImageBackground
      source={PLACEHOLDER_IMAGES.WELCOME_BG}
      style={styles.backgroundImage}
    >
      <StatusBar barStyle="light-content" />
      <SafeAreaView style={styles.container}>
        <View style={styles.logoContainer}>
          <Image
            source={{ uri: PLACEHOLDER_IMAGES.LOGO }}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.appName}>Zakumunda</Text>
          <Text style={styles.tagline}>Farm Fresh Produce Marketplace</Text>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            style={styles.button}
            labelStyle={styles.buttonLabel}
            onPress={() => navigation.navigate(SCREENS.LOGIN)}
          >
            Login
          </Button>

          <Button
            mode="outlined"
            style={[styles.button, styles.signupButton]}
            labelStyle={[styles.buttonLabel, styles.signupButtonLabel]}
            onPress={() => navigation.navigate(SCREENS.REGISTER)}
          >
            Sign Up
          </Button>

          <TouchableOpacity
            style={styles.guestButton}
            onPress={handleGuestMode}
          >
            <Text style={styles.guestButtonText}>Continue as Guest</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    backgroundColor: COLORS.primary, // Fallback color
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.l,
    paddingTop: SPACING.xxl * 2,
    paddingBottom: SPACING.xxl,
    backgroundColor: 'rgba(0, 0, 0, 0.4)', // Overlay for better text visibility
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: SPACING.xxl,
  },
  logo: {
    width: scale(150),
    height: scale(150),
    marginBottom: SPACING.m,
  },
  appName: {
    fontSize: SIZES.title,
    fontWeight: 'bold',
    color: COLORS.white,
    marginBottom: SPACING.xs,
  },
  tagline: {
    fontSize: SIZES.large,
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.l,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: SPACING.xxl,
  },
  button: {
    marginBottom: SPACING.m,
    paddingVertical: SPACING.s,
    borderRadius: 8,
  },
  buttonLabel: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    paddingVertical: SPACING.xs,
  },
  signupButton: {
    backgroundColor: 'transparent',
    borderColor: COLORS.white,
    borderWidth: 2,
  },
  signupButtonLabel: {
    color: COLORS.white,
  },
  guestButton: {
    alignItems: 'center',
    marginTop: SPACING.m,
    paddingVertical: SPACING.s,
  },
  guestButtonText: {
    color: COLORS.white,
    fontSize: SIZES.medium,
    textDecorationLine: 'underline',
  },
});

export default WelcomeScreen;
