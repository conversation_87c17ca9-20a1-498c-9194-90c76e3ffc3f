import React, { createContext, useState, useContext, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define our theme colors
const lightTheme = {
  primary: '#2E7D32',
  secondary: '#F4C430',
  background: '#F5F5F5',
  card: '#FFFFFF',
  text: '#333333',
  border: '#E0E0E0',
  notification: '#FF3B30',
  error: '#FF3B30',
  success: '#4CAF50',
  warning: '#FFC107',
  info: '#2196F3',
  gray: '#757575',
  lightGray: '#F0F0F0',
  white: '#FFFFFF',
  shadow: 'rgba(0, 0, 0, 0.1)',
};

const darkTheme = {
  primary: '#388E3C',
  secondary: '#FFD54F',
  background: '#121212',
  card: '#1E1E1E',
  text: '#F5F5F5',
  border: '#2C2C2C',
  notification: '#FF453A',
  error: '#FF453A',
  success: '#5AD24D',
  warning: '#FFD60A',
  info: '#5AC8FA',
  gray: '#ABABAB',
  lightGray: '#2C2C2C',
  white: '#1E1E1E',
  shadow: 'rgba(0, 0, 0, 0.3)',
};

// Create context
const ThemeContext = createContext();

// Storage key
const THEME_PREFERENCE_KEY = '@theme_preference';

export const ThemeProvider = ({ children }) => {
  const deviceTheme = useColorScheme();
  const [themePreference, setThemePreference] = useState('system'); // 'light', 'dark', or 'system'
  const [colors, setColors] = useState(lightTheme);

  // Load saved theme preference
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedPreference = await AsyncStorage.getItem(THEME_PREFERENCE_KEY);
        if (savedPreference) {
          setThemePreference(savedPreference);
        }
      } catch (error) {
        console.log('Error loading theme preference:', error);
      }
    };
    
    loadThemePreference();
  }, []);

  // Apply theme based on preference
  useEffect(() => {
    const applyTheme = () => {
      if (themePreference === 'system') {
        setColors(deviceTheme === 'dark' ? darkTheme : lightTheme);
      } else {
        setColors(themePreference === 'dark' ? darkTheme : lightTheme);
      }
    };
    
    applyTheme();
  }, [themePreference, deviceTheme]);

  // Set theme preference and save it
  const setTheme = async (preference) => {
    try {
      setThemePreference(preference);
      await AsyncStorage.setItem(THEME_PREFERENCE_KEY, preference);
    } catch (error) {
      console.log('Error saving theme preference:', error);
    }
  };

  // Check if current theme is dark
  const isDarkTheme = 
    (themePreference === 'dark') || 
    (themePreference === 'system' && deviceTheme === 'dark');

  return (
    <ThemeContext.Provider 
      value={{ 
        colors, 
        isDarkTheme,
        themePreference,
        setTheme 
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext; 