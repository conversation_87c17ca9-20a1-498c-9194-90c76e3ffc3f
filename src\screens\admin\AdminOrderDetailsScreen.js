import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
} from 'react-native';
import {
  Button,
  Divider,
  Card,
  IconButton,
  Menu,
  Portal,
  Dialog,
  Paragraph,
  List,
  Avatar,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS } from '../../constants';
import useStore from '../../store/useStore';

const AdminOrderDetailsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { orders, updateOrderStatus } = useStore();
  
  const orderId = route.params?.orderId;
  const order = orders.find(o => o.id === orderId);
  
  const [statusMenuVisible, setStatusMenuVisible] = useState(false);
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [newStatus, setNewStatus] = useState('');

  // Order statuses
  const orderStatuses = [
    { id: 'pending', label: 'Pending', color: COLORS.warning },
    { id: 'processing', label: 'Processing', color: COLORS.info },
    { id: 'shipped', label: 'Shipped', color: COLORS.secondary },
    { id: 'delivered', label: 'Delivered', color: COLORS.success },
    { id: 'cancelled', label: 'Cancelled', color: COLORS.error },
  ];

  // Get status color
  const getStatusColor = (status) => {
    const statusObj = orderStatuses.find(s => s.id === status);
    return statusObj ? statusObj.color : COLORS.gray;
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Handle status update
  const handleStatusUpdate = (status) => {
    setNewStatus(status);
    setStatusMenuVisible(false);
    setConfirmDialogVisible(true);
  };

  // Confirm status update
  const confirmStatusUpdate = () => {
    updateOrderStatus(orderId, newStatus);
    setConfirmDialogVisible(false);
    Alert.alert('Success', 'Order status updated successfully');
  };

  if (!order) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            color={COLORS.white}
            onPress={() => navigation.goBack()}
          />
          <Text style={styles.headerTitle}>Order Details</Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="clipboard-text-off" size={64} color={COLORS.gray} />
          <Text style={styles.emptyText}>Order not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          color={COLORS.white}
          onPress={() => navigation.goBack()}
        />
        <Text style={styles.headerTitle}>Order #{order.id}</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Order Status */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.statusHeader}>
              <View>
                <Text style={styles.sectionTitle}>Status</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
                  <Text style={styles.statusText}>{order.status}</Text>
                </View>
              </View>
              <Menu
                visible={statusMenuVisible}
                onDismiss={() => setStatusMenuVisible(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setStatusMenuVisible(true)}
                    style={styles.updateButton}
                  >
                    Update Status
                  </Button>
                }
              >
                {orderStatuses.map((status) => (
                  <Menu.Item
                    key={status.id}
                    title={status.label}
                    onPress={() => handleStatusUpdate(status.id)}
                  />
                ))}
              </Menu>
            </View>

            <View style={styles.timelineContainer}>
              {orderStatuses.map((status, index) => (
                <View key={status.id} style={styles.timelineItem}>
                  <View style={styles.timelineIconContainer}>
                    <View
                      style={[
                        styles.timelineIcon,
                        {
                          backgroundColor:
                            orderStatuses.findIndex(s => s.id === order.status) >= index
                              ? status.color
                              : COLORS.lightGray,
                        },
                      ]}
                    >
                      <MaterialCommunityIcons
                        name={
                          status.id === 'pending' ? 'clock-outline' :
                          status.id === 'processing' ? 'progress-check' :
                          status.id === 'shipped' ? 'truck-delivery' :
                          status.id === 'delivered' ? 'check-circle' :
                          'close-circle'
                        }
                        size={16}
                        color={COLORS.white}
                      />
                    </View>
                    {index < orderStatuses.length - 1 && (
                      <View
                        style={[
                          styles.timelineLine,
                          {
                            backgroundColor:
                              orderStatuses.findIndex(s => s.id === order.status) > index
                                ? status.color
                                : COLORS.lightGray,
                          },
                        ]}
                      />
                    )}
                  </View>
                  <View style={styles.timelineContent}>
                    <Text style={styles.timelineTitle}>{status.label}</Text>
                    {order.status === status.id && (
                      <Text style={styles.timelineDate}>
                        {formatDate(order.updatedAt)}
                      </Text>
                    )}
                  </View>
                </View>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Customer Information */}
        <Card style={styles.card}>
          <Card.Title title="Customer Information" />
          <Card.Content>
            <List.Item
              title={order.customer.name}
              description="Customer"
              left={props => <List.Icon {...props} icon="account" />}
            />
            <Divider style={styles.divider} />
            <List.Item
              title={order.customer.email}
              description="Email"
              left={props => <List.Icon {...props} icon="email" />}
            />
            <Divider style={styles.divider} />
            <List.Item
              title={order.customer.phone}
              description="Phone"
              left={props => <List.Icon {...props} icon="phone" />}
            />
            <Divider style={styles.divider} />
            <List.Item
              title={order.customer.address}
              description="Address"
              left={props => <List.Icon {...props} icon="map-marker" />}
            />
          </Card.Content>
        </Card>

        {/* Order Items */}
        <Card style={styles.card}>
          <Card.Title title="Order Items" />
          <Card.Content>
            {order.items.map((item, index) => (
              <View key={index}>
                <View style={styles.orderItem}>
                  <Image
                    source={{ uri: item.product.image }}
                    style={styles.productImage}
                  />
                  <View style={styles.productInfo}>
                    <Text style={styles.productName}>{item.product.name}</Text>
                    <Text style={styles.productPrice}>
                      ${item.price.toFixed(2)} / {item.product.unit}
                    </Text>
                    <Text style={styles.productQuantity}>
                      Quantity: {item.quantity}
                    </Text>
                  </View>
                  <Text style={styles.itemTotal}>
                    ${(item.price * item.quantity).toFixed(2)}
                  </Text>
                </View>
                {index < order.items.length - 1 && <Divider style={styles.divider} />}
              </View>
            ))}
          </Card.Content>
        </Card>

        {/* Order Summary */}
        <Card style={styles.card}>
          <Card.Title title="Order Summary" />
          <Card.Content>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Order Date</Text>
              <Text style={styles.summaryValue}>{formatDate(order.createdAt)}</Text>
            </View>
            <Divider style={styles.divider} />
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Payment Method</Text>
              <Text style={styles.summaryValue}>
                {order.paymentMethod === 'card' ? 'Card Payment' :
                 order.paymentMethod === 'cash' ? 'Cash on Delivery' : 'Mobile Money'}
              </Text>
            </View>
            <Divider style={styles.divider} />
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Items Total</Text>
              <Text style={styles.summaryValue}>
                ${order.total.toFixed(2)}
              </Text>
            </View>
            <Divider style={styles.divider} />
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Delivery Fee</Text>
              <Text style={styles.summaryValue}>$0.00</Text>
            </View>
            <Divider style={styles.divider} />
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, styles.totalLabel]}>Total</Text>
              <Text style={[styles.summaryValue, styles.totalValue]}>
                ${order.total.toFixed(2)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        <View style={styles.spacer} />
      </ScrollView>

      {/* Confirm Status Update Dialog */}
      <Portal>
        <Dialog
          visible={confirmDialogVisible}
          onDismiss={() => setConfirmDialogVisible(false)}
        >
          <Dialog.Title>Update Order Status</Dialog.Title>
          <Dialog.Content>
            <Paragraph>
              Are you sure you want to update the order status to{' '}
              <Text style={{ fontWeight: 'bold' }}>
                {orderStatuses.find(s => s.id === newStatus)?.label}
              </Text>
              ?
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setConfirmDialogVisible(false)}>Cancel</Button>
            <Button onPress={confirmStatusUpdate}>Update</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  scrollView: {
    flex: 1,
    padding: SPACING.m,
  },
  card: {
    marginBottom: SPACING.m,
    ...SHADOWS.small,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.m,
  },
  sectionTitle: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    marginBottom: SPACING.xs,
  },
  statusBadge: {
    paddingHorizontal: SPACING.s,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  statusText: {
    color: COLORS.white,
    fontSize: SIZES.small,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  updateButton: {
    borderColor: COLORS.primary,
  },
  timelineContainer: {
    marginTop: SPACING.m,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: SPACING.m,
  },
  timelineIconContainer: {
    alignItems: 'center',
    marginRight: SPACING.m,
  },
  timelineIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.lightGray,
  },
  timelineLine: {
    width: 2,
    height: 30,
    backgroundColor: COLORS.lightGray,
  },
  timelineContent: {
    flex: 1,
  },
  timelineTitle: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
  },
  timelineDate: {
    fontSize: SIZES.small,
    color: COLORS.gray,
    marginTop: 2,
  },
  divider: {
    marginVertical: SPACING.s,
  },
  orderItem: {
    flexDirection: 'row',
    marginVertical: SPACING.s,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 4,
  },
  productInfo: {
    flex: 1,
    marginLeft: SPACING.m,
  },
  productName: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
  },
  productPrice: {
    fontSize: SIZES.small,
    color: COLORS.gray,
    marginTop: 2,
  },
  productQuantity: {
    fontSize: SIZES.small,
    marginTop: 2,
  },
  itemTotal: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
  },
  summaryLabel: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
  summaryValue: {
    fontSize: SIZES.medium,
    color: COLORS.text,
  },
  totalLabel: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  totalValue: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  spacer: {
    height: 40,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.m,
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
});

export default AdminOrderDetailsScreen;
