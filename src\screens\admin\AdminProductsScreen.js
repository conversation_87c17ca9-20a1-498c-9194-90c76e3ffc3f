import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  ScrollView,
} from 'react-native';
import {
  <PERSON><PERSON>,
  FAB,
  Chip,
  <PERSON>ton,
  <PERSON>u,
  Di<PERSON>r,
  Icon<PERSON>utton,
  Segmented<PERSON><PERSON><PERSON>,
  Card,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS, CATEGORIES } from '../../constants';
import useStore from '../../store/useStore';

const AdminProductsScreen = () => {
  const navigation = useNavigation();
  const { products, filteredProducts, searchProducts, filterProducts, deleteProduct } = useStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [viewMode, setViewMode] = useState('grid');
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);

  // Handle search
  const onChangeSearch = (query) => {
    setSearchQuery(query);
    if (query.trim() === '') {
      filterProducts(selectedCategory);
    } else {
      searchProducts(query);
    }
  };

  // Handle category filter
  const handleCategoryFilter = (categoryId) => {
    setSelectedCategory(categoryId === selectedCategory ? null : categoryId);
    filterProducts(categoryId === selectedCategory ? null : categoryId);
  };

  // Handle delete product
  const handleDeleteProduct = (productId) => {
    Alert.alert(
      'Delete Product',
      'Are you sure you want to delete this product?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => {
            deleteProduct(productId);
            setMenuVisible(false);
          },
          style: 'destructive',
        },
      ]
    );
  };

  // Render product item in grid view
  const renderGridItem = ({ item }) => {
    const category = CATEGORIES.find(cat => cat.id === item.category);

    return (
      <Card style={styles.gridCard}>
        <TouchableOpacity
          onPress={() => navigation.navigate(SCREENS.ADMIN_PRODUCT_FORM, { productId: item.id })}
        >
          <Card.Cover
            source={{ uri: item.image }}
            style={styles.gridImage}
          />
          <Card.Content style={styles.gridContent}>
            <Text style={styles.gridTitle} numberOfLines={1}>{item.name}</Text>
            <Text style={styles.gridPrice}>${item.price.toFixed(2)} / {item.unit}</Text>
            <View style={styles.gridFooter}>
              <Chip
                style={styles.categoryChip}
                textStyle={styles.categoryChipText}
              >
                {category?.name || 'Unknown'}
              </Chip>
              <Text style={styles.stockText}>
                Stock: {item.stock}
              </Text>
            </View>
          </Card.Content>
        </TouchableOpacity>
        <Card.Actions style={styles.cardActions}>
          <IconButton
            icon="pencil"
            size={20}
            onPress={() => navigation.navigate(SCREENS.ADMIN_PRODUCT_FORM, { productId: item.id })}
          />
          <IconButton
            icon="delete"
            size={20}
            onPress={() => handleDeleteProduct(item.id)}
          />
        </Card.Actions>
      </Card>
    );
  };

  // Render product item in list view
  const renderListItem = ({ item }) => {
    const category = CATEGORIES.find(cat => cat.id === item.category);

    return (
      <Card style={styles.listCard}>
        <View style={styles.listItem}>
          <Image
            source={{ uri: item.image }}
            style={styles.listImage}
          />
          <View style={styles.listContent}>
            <Text style={styles.listTitle} numberOfLines={1}>{item.name}</Text>
            <Text style={styles.listPrice}>${item.price.toFixed(2)} / {item.unit}</Text>
            <View style={styles.listFooter}>
              <Chip
                style={styles.categoryChip}
                textStyle={styles.categoryChipText}
              >
                {category?.name || 'Unknown'}
              </Chip>
              <Text style={styles.stockText}>
                Stock: {item.stock}
              </Text>
            </View>
          </View>
          <View style={styles.listActions}>
            <IconButton
              icon="pencil"
              size={20}
              onPress={() => navigation.navigate(SCREENS.ADMIN_PRODUCT_FORM, { productId: item.id })}
            />
            <IconButton
              icon="delete"
              size={20}
              onPress={() => handleDeleteProduct(item.id)}
            />
          </View>
        </View>
      </Card>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Products</Text>
        <SegmentedButtons
          value={viewMode}
          onValueChange={setViewMode}
          buttons={[
            { value: 'grid', icon: 'grid' },
            { value: 'list', icon: 'format-list-bulleted' },
          ]}
          style={styles.viewToggle}
        />
      </View>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search products"
          onChangeText={onChangeSearch}
          value={searchQuery}
          style={styles.searchBar}
        />
      </View>

      <View style={styles.categoriesContainer}>
        <ScrollingCategories
          categories={CATEGORIES}
          selectedCategory={selectedCategory}
          onSelectCategory={handleCategoryFilter}
        />
      </View>

      <FlatList
        data={filteredProducts}
        renderItem={viewMode === 'grid' ? renderGridItem : renderListItem}
        keyExtractor={(item) => item.id}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode === 'grid' ? 'grid' : 'list'}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons name="package-variant" size={64} color={COLORS.gray} />
            <Text style={styles.emptyText}>No products found</Text>
          </View>
        }
      />

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => navigation.navigate(SCREENS.ADMIN_PRODUCT_FORM)}
      />
    </SafeAreaView>
  );
};

// Scrolling Categories Component
const ScrollingCategories = ({ categories, selectedCategory, onSelectCategory }) => {
  return (
    <FlatList
      data={categories}
      horizontal
      showsHorizontalScrollIndicator={false}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <Chip
          selected={selectedCategory === item.id}
          onPress={() => onSelectCategory(item.id)}
          style={[
            styles.categoryChip,
            selectedCategory === item.id && styles.selectedCategoryChip
          ]}
          textStyle={[
            styles.categoryChipText,
            selectedCategory === item.id && styles.selectedCategoryChipText
          ]}
        >
          {item.name}
        </Chip>
      )}
      contentContainerStyle={styles.categoriesList}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  viewToggle: {
    backgroundColor: 'transparent',
    borderColor: COLORS.white,
  },
  searchContainer: {
    padding: SPACING.m,
    backgroundColor: COLORS.white,
    ...SHADOWS.small,
  },
  searchBar: {
    elevation: 0,
    backgroundColor: COLORS.lightGray,
  },
  categoriesContainer: {
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.s,
    ...SHADOWS.small,
  },
  categoriesList: {
    paddingHorizontal: SPACING.m,
  },
  categoryChip: {
    marginRight: SPACING.s,
    backgroundColor: COLORS.lightGray,
  },
  selectedCategoryChip: {
    backgroundColor: COLORS.primary,
  },
  categoryChipText: {
    color: COLORS.text,
  },
  selectedCategoryChipText: {
    color: COLORS.white,
  },
  listContainer: {
    padding: SPACING.m,
  },
  gridCard: {
    flex: 1,
    margin: SPACING.xs,
    maxWidth: '48%',
    ...SHADOWS.small,
  },
  gridImage: {
    height: 120,
  },
  gridContent: {
    padding: SPACING.s,
  },
  gridTitle: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    marginBottom: SPACING.xs,
  },
  gridPrice: {
    fontSize: SIZES.small,
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  gridFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: SPACING.xs,
  },
  stockText: {
    fontSize: SIZES.xSmall,
    color: COLORS.gray,
  },
  cardActions: {
    justifyContent: 'flex-end',
    paddingTop: 0,
  },
  listCard: {
    marginBottom: SPACING.s,
    ...SHADOWS.small,
  },
  listItem: {
    flexDirection: 'row',
    padding: SPACING.s,
  },
  listImage: {
    width: 80,
    height: 80,
    borderRadius: 4,
  },
  listContent: {
    flex: 1,
    marginLeft: SPACING.m,
    justifyContent: 'center',
  },
  listTitle: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
  },
  listPrice: {
    fontSize: SIZES.small,
    color: COLORS.primary,
    fontWeight: 'bold',
    marginVertical: SPACING.xs,
  },
  listFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listActions: {
    justifyContent: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.m,
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
  fab: {
    position: 'absolute',
    margin: SPACING.m,
    right: 0,
    bottom: 0,
    backgroundColor: COLORS.primary,
  },
});

export default AdminProductsScreen;
