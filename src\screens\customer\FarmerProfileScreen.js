import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  FlatList,
} from 'react-native';
import {
  Avatar,
  Button,
  Divider,
  Card,
  Title,
  Paragraph,
  IconButton,
  Chip,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS, SAMPLE_PRODUCTS } from '../../constants';
import useStore from '../../store/useStore';

const FarmerProfileScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { farmerId } = route.params || {};
  
  // Get products by farmer
  const farmerProducts = SAMPLE_PRODUCTS.filter(product => product.farmer.id === farmerId);
  
  // Use first product's farmer info if available, otherwise use fallback
  const farmer = farmerProducts.length > 0 
    ? farmerProducts[0].farmer 
    : {
        id: 'unknown',
        name: '<PERSON>ple Farmer',
        location: 'Unknown Location',
        rating: 4.5,
        description: 'This farmer specializes in organic farming techniques and sustainable agriculture. Their produce is grown without harmful pesticides and with respect for the environment.',
        image: 'https://randomuser.me/api/portraits/men/32.jpg',
        backgroundImage: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2532&q=80',
      };

  // Render rating stars
  const renderRatingStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <MaterialCommunityIcons
          key={i}
          name={i <= rating ? 'star' : 'star-outline'}
          size={18}
          color={i <= rating ? COLORS.secondary : COLORS.gray}
          style={{ marginRight: 2 }}
        />
      );
    }
    return stars;
  };

  // Render product item
  const renderProductItem = ({ item }) => (
    <TouchableOpacity
      style={styles.productCard}
      onPress={() => navigation.navigate(SCREENS.PRODUCT_DETAILS, { productId: item.id })}
    >
      <Image source={{ uri: item.image }} style={styles.productImage} />
      <View style={styles.productInfo}>
        <Text style={styles.productName} numberOfLines={1}>{item.name}</Text>
        <Text style={styles.productPrice}>${item.price.toFixed(2)} / {item.unit}</Text>
        <View style={styles.ratingContainer}>
          {renderRatingStars(item.rating)}
          <Text style={styles.ratingText}>({item.rating})</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.primary} />
      
      {/* Header with Background Image */}
      <View style={styles.headerContainer}>
        <Image
          source={{ uri: farmer.backgroundImage || 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2532&q=80' }}
          style={styles.headerBackground}
        />
        <View style={styles.headerOverlay} />
        <IconButton
          icon="arrow-left"
          color={COLORS.white}
          size={24}
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        />
      </View>

      {/* Farmer Info */}
      <View style={styles.farmerInfoContainer}>
        <Avatar.Image
          size={100}
          source={{ uri: farmer.image || 'https://randomuser.me/api/portraits/men/32.jpg' }}
          style={styles.farmerAvatar}
        />
        <Text style={styles.farmerName}>{farmer.name}</Text>
        <View style={styles.locationContainer}>
          <MaterialCommunityIcons name="map-marker" size={16} color={COLORS.gray} />
          <Text style={styles.locationText}>{farmer.location}</Text>
        </View>
        <View style={styles.ratingContainer}>
          {renderRatingStars(farmer.rating)}
          <Text style={styles.ratingText}>({farmer.rating})</Text>
        </View>
        <TouchableOpacity 
          style={styles.messageButton}
          onPress={() => alert('Messaging feature not implemented in this demo')}
        >
          <MaterialCommunityIcons name="message-text" size={16} color={COLORS.white} />
          <Text style={styles.messageButtonText}>Message</Text>
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* About Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>About</Title>
            <Paragraph style={styles.aboutText}>
              {farmer.description || 'This farmer specializes in organic farming techniques and sustainable agriculture. Their produce is grown without harmful pesticides and with respect for the environment.'}
            </Paragraph>
          </Card.Content>
        </Card>

        {/* Products Section */}
        <View style={styles.productsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Products</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          
          {farmerProducts.length > 0 ? (
            <FlatList
              data={farmerProducts}
              renderItem={renderProductItem}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.productsList}
            />
          ) : (
            <Text style={styles.noProductsText}>No products available from this farmer.</Text>
          )}
        </View>

        {/* Categories Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Categories</Title>
            <View style={styles.categoriesContainer}>
              <Chip style={styles.categoryChip} mode="outlined">Vegetables</Chip>
              <Chip style={styles.categoryChip} mode="outlined">Fruits</Chip>
              <Chip style={styles.categoryChip} mode="outlined">Organic</Chip>
              <Chip style={styles.categoryChip} mode="outlined">Local</Chip>
            </View>
          </Card.Content>
        </Card>

        {/* Contact Info Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Contact Information</Title>
            <View style={styles.contactItem}>
              <MaterialCommunityIcons name="email" size={20} color={COLORS.primary} />
              <Text style={styles.contactText}><EMAIL></Text>
            </View>
            <View style={styles.contactItem}>
              <MaterialCommunityIcons name="phone" size={20} color={COLORS.primary} />
              <Text style={styles.contactText}>****** 567 890</Text>
            </View>
            <View style={styles.contactItem}>
              <MaterialCommunityIcons name="web" size={20} color={COLORS.primary} />
              <Text style={styles.contactText}>www.farmwebsite.com</Text>
            </View>
          </Card.Content>
        </Card>

        <View style={styles.bottomSpacer} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  headerContainer: {
    height: 150,
    position: 'relative',
  },
  headerBackground: {
    width: '100%',
    height: '100%',
  },
  headerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  backButton: {
    position: 'absolute',
    top: 10,
    left: 10,
  },
  farmerInfoContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.l,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
    ...SHADOWS.small,
  },
  farmerAvatar: {
    marginTop: -70,
    borderWidth: 4,
    borderColor: COLORS.white,
  },
  farmerName: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SPACING.s,
    marginBottom: SPACING.xs,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  locationText: {
    fontSize: SIZES.small,
    color: COLORS.gray,
    marginLeft: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.m,
  },
  ratingText: {
    fontSize: SIZES.small,
    color: COLORS.text,
    marginLeft: SPACING.xs,
  },
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.m,
    borderRadius: 20,
  },
  messageButtonText: {
    color: COLORS.white,
    fontWeight: 'bold',
    marginLeft: SPACING.xs,
  },
  card: {
    marginHorizontal: SPACING.m,
    marginVertical: SPACING.s,
    ...SHADOWS.small,
  },
  sectionTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.s,
  },
  aboutText: {
    fontSize: SIZES.medium,
    color: COLORS.text,
    lineHeight: 22,
  },
  productsSection: {
    backgroundColor: COLORS.white,
    padding: SPACING.m,
    marginVertical: SPACING.s,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.m,
  },
  viewAllText: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  productsList: {
    paddingVertical: SPACING.s,
  },
  productCard: {
    width: 150,
    marginRight: SPACING.m,
    backgroundColor: COLORS.white,
    borderRadius: 10,
    overflow: 'hidden',
    ...SHADOWS.small,
  },
  productImage: {
    width: '100%',
    height: 120,
  },
  productInfo: {
    padding: SPACING.s,
  },
  productName: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    marginBottom: SPACING.xs,
  },
  productPrice: {
    fontSize: SIZES.small,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.xs,
  },
  noProductsText: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
    textAlign: 'center',
    padding: SPACING.m,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  categoryChip: {
    margin: SPACING.xs,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: SPACING.xs,
  },
  contactText: {
    fontSize: SIZES.medium,
    color: COLORS.text,
    marginLeft: SPACING.s,
  },
  bottomSpacer: {
    height: 40,
  },
});

export default FarmerProfileScreen; 