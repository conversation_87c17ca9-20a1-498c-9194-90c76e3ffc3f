import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  TextInput,
  Button,
  Divider,
  Chip,
  HelperText,
  IconButton,
  Menu,
  Portal,
  Dialog,
  Paragraph,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';
import { Formik } from 'formik';
import * as Yup from 'yup';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../constants/theme';
import { SCREENS, CATEGORIES } from '../../constants';
import useStore from '../../store/useStore';

// Validation schema
const ProductSchema = Yup.object().shape({
  name: Yup.string().required('Product name is required'),
  description: Yup.string().required('Description is required'),
  price: Yup.number()
    .required('Price is required')
    .positive('Price must be positive'),
  stock: Yup.number()
    .required('Stock quantity is required')
    .integer('Stock must be a whole number')
    .min(0, 'Stock cannot be negative'),
  unit: Yup.string().required('Unit is required'),
  category: Yup.string().required('Category is required'),
  image: Yup.string().required('Product image is required'),
  farmer: Yup.object().shape({
    id: Yup.string().required('Farmer ID is required'),
    name: Yup.string().required('Farmer name is required'),
  }),
});

const AdminProductFormScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { products, addProduct, updateProduct } = useStore();
  
  const productId = route.params?.productId;
  const isEditing = !!productId;
  
  // Find the product if editing
  const product = isEditing
    ? products.find(p => p.id === productId)
    : null;
  
  const [categoryMenuVisible, setCategoryMenuVisible] = useState(false);
  const [unitMenuVisible, setUnitMenuVisible] = useState(false);
  const [discardDialogVisible, setDiscardDialogVisible] = useState(false);
  
  // Available units
  const units = ['kg', 'g', 'lb', 'piece', 'bunch', 'box', 'bag', 'liter'];

  // Handle image picking
  const pickImage = async (setFieldValue) => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Sorry, we need camera roll permissions to upload images.');
      return;
    }
    
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });
    
    if (!result.canceled) {
      setFieldValue('image', result.assets[0].uri);
    }
  };

  // Handle form submission
  const handleSubmit = (values) => {
    if (isEditing) {
      updateProduct(productId, values);
      Alert.alert('Success', 'Product updated successfully');
    } else {
      addProduct(values);
      Alert.alert('Success', 'Product added successfully');
    }
    navigation.goBack();
  };

  // Handle discard changes
  const handleDiscard = () => {
    setDiscardDialogVisible(false);
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            color={COLORS.white}
            onPress={() => setDiscardDialogVisible(true)}
          />
          <Text style={styles.headerTitle}>
            {isEditing ? 'Edit Product' : 'Add Product'}
          </Text>
          <View style={{ width: 40 }} />
        </View>

        <Formik
          initialValues={
            product || {
              name: '',
              description: '',
              price: '',
              stock: '',
              unit: 'kg',
              category: '',
              image: '',
              farmer: {
                id: '1', // Default farmer for demo
                name: 'John Farmer',
                location: 'Kampala East',
                rating: 4.8,
              },
            }
          }
          validationSchema={ProductSchema}
          onSubmit={handleSubmit}
        >
          {({
            handleChange,
            handleBlur,
            handleSubmit,
            values,
            errors,
            touched,
            setFieldValue,
            isValid,
            dirty,
          }) => (
            <>
              <ScrollView style={styles.scrollView}>
                {/* Image Upload */}
                <TouchableOpacity
                  style={styles.imageUploadContainer}
                  onPress={() => pickImage(setFieldValue)}
                >
                  {values.image ? (
                    <Image source={{ uri: values.image }} style={styles.productImage} />
                  ) : (
                    <View style={styles.imagePlaceholder}>
                      <MaterialCommunityIcons name="camera" size={40} color={COLORS.gray} />
                      <Text style={styles.imagePlaceholderText}>Tap to add image</Text>
                    </View>
                  )}
                </TouchableOpacity>
                {touched.image && errors.image && (
                  <HelperText type="error" visible={true}>
                    {errors.image}
                  </HelperText>
                )}

                {/* Product Name */}
                <TextInput
                  label="Product Name"
                  value={values.name}
                  onChangeText={handleChange('name')}
                  onBlur={handleBlur('name')}
                  style={styles.input}
                  error={touched.name && errors.name}
                />
                {touched.name && errors.name && (
                  <HelperText type="error" visible={true}>
                    {errors.name}
                  </HelperText>
                )}

                {/* Category */}
                <Menu
                  visible={categoryMenuVisible}
                  onDismiss={() => setCategoryMenuVisible(false)}
                  anchor={
                    <TouchableOpacity
                      onPress={() => setCategoryMenuVisible(true)}
                    >
                      <TextInput
                        label="Category"
                        value={
                          values.category
                            ? CATEGORIES.find(cat => cat.id === values.category)?.name || ''
                            : ''
                        }
                        style={styles.input}
                        editable={false}
                        right={<TextInput.Icon icon="menu-down" />}
                        error={touched.category && errors.category}
                      />
                    </TouchableOpacity>
                  }
                >
                  {CATEGORIES.map((category) => (
                    <Menu.Item
                      key={category.id}
                      title={category.name}
                      onPress={() => {
                        setFieldValue('category', category.id);
                        setCategoryMenuVisible(false);
                      }}
                    />
                  ))}
                </Menu>
                {touched.category && errors.category && (
                  <HelperText type="error" visible={true}>
                    {errors.category}
                  </HelperText>
                )}

                {/* Price and Unit */}
                <View style={styles.row}>
                  <View style={styles.flex2}>
                    <TextInput
                      label="Price"
                      value={values.price.toString()}
                      onChangeText={(text) => {
                        // Allow only numbers and decimal point
                        const regex = /^[0-9]*\.?[0-9]*$/;
                        if (regex.test(text)) {
                          setFieldValue('price', text);
                        }
                      }}
                      onBlur={handleBlur('price')}
                      style={styles.input}
                      keyboardType="numeric"
                      left={<TextInput.Affix text="$" />}
                      error={touched.price && errors.price}
                    />
                    {touched.price && errors.price && (
                      <HelperText type="error" visible={true}>
                        {errors.price}
                      </HelperText>
                    )}
                  </View>

                  <View style={styles.flex1}>
                    <Menu
                      visible={unitMenuVisible}
                      onDismiss={() => setUnitMenuVisible(false)}
                      anchor={
                        <TouchableOpacity
                          onPress={() => setUnitMenuVisible(true)}
                        >
                          <TextInput
                            label="Unit"
                            value={values.unit}
                            style={styles.input}
                            editable={false}
                            right={<TextInput.Icon icon="menu-down" />}
                            error={touched.unit && errors.unit}
                          />
                        </TouchableOpacity>
                      }
                    >
                      {units.map((unit) => (
                        <Menu.Item
                          key={unit}
                          title={unit}
                          onPress={() => {
                            setFieldValue('unit', unit);
                            setUnitMenuVisible(false);
                          }}
                        />
                      ))}
                    </Menu>
                    {touched.unit && errors.unit && (
                      <HelperText type="error" visible={true}>
                        {errors.unit}
                      </HelperText>
                    )}
                  </View>
                </View>

                {/* Stock */}
                <TextInput
                  label="Stock Quantity"
                  value={values.stock.toString()}
                  onChangeText={(text) => {
                    // Allow only numbers
                    const regex = /^[0-9]*$/;
                    if (regex.test(text)) {
                      setFieldValue('stock', text);
                    }
                  }}
                  onBlur={handleBlur('stock')}
                  style={styles.input}
                  keyboardType="numeric"
                  error={touched.stock && errors.stock}
                />
                {touched.stock && errors.stock && (
                  <HelperText type="error" visible={true}>
                    {errors.stock}
                  </HelperText>
                )}

                {/* Description */}
                <TextInput
                  label="Description"
                  value={values.description}
                  onChangeText={handleChange('description')}
                  onBlur={handleBlur('description')}
                  style={styles.input}
                  multiline
                  numberOfLines={4}
                  error={touched.description && errors.description}
                />
                {touched.description && errors.description && (
                  <HelperText type="error" visible={true}>
                    {errors.description}
                  </HelperText>
                )}

                {/* Farmer Info (Read-only for demo) */}
                <View style={styles.farmerContainer}>
                  <Text style={styles.sectionTitle}>Farmer Information</Text>
                  <View style={styles.farmerInfo}>
                    <Text style={styles.farmerName}>{values.farmer.name}</Text>
                    <Text style={styles.farmerLocation}>{values.farmer.location}</Text>
                    <View style={styles.ratingContainer}>
                      <MaterialCommunityIcons name="star" size={16} color={COLORS.secondary} />
                      <Text style={styles.ratingText}>{values.farmer.rating}</Text>
                    </View>
                  </View>
                </View>

                <View style={styles.spacer} />
              </ScrollView>

              <View style={styles.footer}>
                <Button
                  mode="outlined"
                  onPress={() => setDiscardDialogVisible(true)}
                  style={styles.cancelButton}
                >
                  Cancel
                </Button>
                <Button
                  mode="contained"
                  onPress={handleSubmit}
                  style={styles.saveButton}
                  disabled={!(isValid && dirty)}
                >
                  {isEditing ? 'Update' : 'Save'}
                </Button>
              </View>
            </>
          )}
        </Formik>

        {/* Discard Changes Dialog */}
        <Portal>
          <Dialog
            visible={discardDialogVisible}
            onDismiss={() => setDiscardDialogVisible(false)}
          >
            <Dialog.Title>Discard Changes?</Dialog.Title>
            <Dialog.Content>
              <Paragraph>
                Are you sure you want to discard your changes? This action cannot be undone.
              </Paragraph>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setDiscardDialogVisible(false)}>Cancel</Button>
              <Button onPress={handleDiscard}>Discard</Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  scrollView: {
    flex: 1,
    padding: SPACING.m,
  },
  imageUploadContainer: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: SPACING.m,
    ...SHADOWS.medium,
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: COLORS.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePlaceholderText: {
    marginTop: SPACING.s,
    color: COLORS.gray,
  },
  input: {
    marginBottom: SPACING.s,
    backgroundColor: COLORS.white,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  flex1: {
    flex: 1,
    marginLeft: SPACING.s,
  },
  flex2: {
    flex: 2,
  },
  farmerContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 8,
    padding: SPACING.m,
    marginTop: SPACING.m,
    ...SHADOWS.small,
  },
  sectionTitle: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    marginBottom: SPACING.s,
  },
  farmerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  farmerName: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
  },
  farmerLocation: {
    fontSize: SIZES.small,
    color: COLORS.gray,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: 2,
    color: COLORS.text,
  },
  spacer: {
    height: 100,
  },
  footer: {
    flexDirection: 'row',
    padding: SPACING.m,
    backgroundColor: COLORS.white,
    ...SHADOWS.medium,
  },
  cancelButton: {
    flex: 1,
    marginRight: SPACING.s,
    borderColor: COLORS.primary,
  },
  saveButton: {
    flex: 2,
    backgroundColor: COLORS.primary,
  },
});

export default AdminProductFormScreen;
