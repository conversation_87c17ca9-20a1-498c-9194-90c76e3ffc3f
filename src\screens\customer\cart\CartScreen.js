import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import {
  Icon<PERSON>utton,
  Button,
  Divider,
  Card,
  Title,
  Paragraph,
  Badge,
  List,
  Avatar,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { SwipeListView } from 'react-native-swipe-list-view';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../../constants/theme';
import { SCREENS } from '../../../constants';
import useStore from '../../../store/useStore';

const CartScreen = () => {
  const navigation = useNavigation();
  const { cart, cartTotal, updateCartItemQuantity, removeFromCart, clearCart } = useStore();

  // Handle quantity change
  const handleQuantityChange = (productId, quantity, currentQuantity) => {
    const newQuantity = Math.max(1, currentQuantity + quantity);
    updateCartItemQuantity(productId, newQuantity);
  };

  // Handle remove item
  const handleRemoveItem = (productId) => {
    Alert.alert(
      'Remove Item',
      'Are you sure you want to remove this item from your cart?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          onPress: () => removeFromCart(productId),
          style: 'destructive',
        },
      ]
    );
  };

  // Handle clear cart
  const handleClearCart = () => {
    if (cart.length === 0) return;
    
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to clear your cart?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          onPress: () => clearCart(),
          style: 'destructive',
        },
      ]
    );
  };

  // Handle checkout
  const handleCheckout = () => {
    if (cart.length === 0) {
      Alert.alert('Empty Cart', 'Your cart is empty. Add some items before checkout.');
      return;
    }
    
    navigation.navigate(SCREENS.CHECKOUT);
  };

  // Render cart item
  const renderCartItem = ({ item }) => (
    <Card style={styles.cartItemCard}>
      <View style={styles.cartItem}>
        <Image source={{ uri: item.image }} style={styles.itemImage} />
        <View style={styles.itemDetails}>
          <Text style={styles.itemName} numberOfLines={1}>{item.name}</Text>
          <Text style={styles.itemPrice}>${item.price.toFixed(2)} / {item.unit}</Text>
          <View style={styles.quantityContainer}>
            <IconButton
              icon="minus"
              size={20}
              color={COLORS.text}
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(item.id, -1, item.quantity)}
              disabled={item.quantity <= 1}
            />
            <Text style={styles.quantityText}>{item.quantity}</Text>
            <IconButton
              icon="plus"
              size={20}
              color={COLORS.text}
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(item.id, 1, item.quantity)}
            />
          </View>
        </View>
        <View style={styles.itemTotal}>
          <Text style={styles.totalText}>${(item.price * item.quantity).toFixed(2)}</Text>
          <IconButton
            icon="delete"
            size={20}
            color={COLORS.error}
            onPress={() => handleRemoveItem(item.id)}
          />
        </View>
      </View>
    </Card>
  );

  // Render hidden item (swipe actions)
  const renderHiddenItem = ({ item }) => (
    <View style={styles.hiddenItemContainer}>
      <TouchableOpacity
        style={[styles.hiddenButton, styles.deleteButton]}
        onPress={() => handleRemoveItem(item.id)}
      >
        <MaterialCommunityIcons name="delete" size={24} color={COLORS.white} />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => navigation.goBack()}
        />
        <Text style={styles.headerTitle}>My Cart</Text>
        <IconButton
          icon="delete"
          size={24}
          onPress={handleClearCart}
          disabled={cart.length === 0}
        />
      </View>

      {/* Cart Items */}
      {cart.length > 0 ? (
        <SwipeListView
          data={cart}
          renderItem={renderCartItem}
          renderHiddenItem={renderHiddenItem}
          rightOpenValue={-75}
          disableLeftSwipe={false}
          disableRightSwipe={true}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.cartList}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="cart-off" size={64} color={COLORS.gray} />
          <Text style={styles.emptyText}>Your cart is empty</Text>
          <Button
            mode="contained"
            style={styles.shopNowButton}
            onPress={() => navigation.navigate(SCREENS.HOME)}
          >
            Shop Now
          </Button>
        </View>
      )}

      {/* Cart Summary */}
      {cart.length > 0 && (
        <View style={styles.summaryContainer}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Subtotal</Text>
            <Text style={styles.summaryValue}>${cartTotal.toFixed(2)}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Delivery Fee</Text>
            <Text style={styles.summaryValue}>$0.00</Text>
          </View>
          <Divider style={styles.divider} />
          <View style={styles.summaryRow}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>${cartTotal.toFixed(2)}</Text>
          </View>
          <Button
            mode="contained"
            style={styles.checkoutButton}
            labelStyle={styles.checkoutButtonLabel}
            onPress={handleCheckout}
          >
            Proceed to Checkout
          </Button>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.s,
    paddingVertical: SPACING.s,
    ...SHADOWS.small,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  cartList: {
    padding: SPACING.m,
  },
  cartItemCard: {
    marginBottom: SPACING.s,
    ...SHADOWS.small,
  },
  cartItem: {
    flexDirection: 'row',
    padding: SPACING.m,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  itemDetails: {
    flex: 1,
    marginLeft: SPACING.m,
    justifyContent: 'space-between',
  },
  itemName: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  itemPrice: {
    fontSize: SIZES.small,
    color: COLORS.gray,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.xs,
  },
  quantityButton: {
    margin: 0,
    backgroundColor: COLORS.lightGray,
  },
  quantityText: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    paddingHorizontal: SPACING.s,
  },
  itemTotal: {
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  totalText: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  hiddenItemContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    marginBottom: SPACING.s,
  },
  hiddenButton: {
    width: 75,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    backgroundColor: COLORS.error,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.m,
    marginBottom: SPACING.l,
    fontSize: SIZES.large,
    color: COLORS.gray,
  },
  shopNowButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.xl,
  },
  summaryContainer: {
    backgroundColor: COLORS.white,
    padding: SPACING.m,
    ...SHADOWS.medium,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: SPACING.xs,
  },
  summaryLabel: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
  summaryValue: {
    fontSize: SIZES.medium,
    color: COLORS.text,
  },
  divider: {
    marginVertical: SPACING.s,
  },
  totalLabel: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  totalValue: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  checkoutButton: {
    marginTop: SPACING.m,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
  },
  checkoutButtonLabel: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    paddingVertical: SPACING.xs,
  },
});

export default CartScreen;
