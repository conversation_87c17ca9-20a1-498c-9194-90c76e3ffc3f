// Simple script to check specific tables for phone numbers
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://saxcjcqohvabropicrkk.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNheGNqY3FvaHZhYnJvcGljcmtrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2MTYxNzUsImV4cCI6MjA2MjE5MjE3NX0.ONVChVWSjr4U185BjJgxK8o6JhIxZ2Z46RAL6xxvQVc';
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkProfilesTable() {
  console.log('\n--- Checking Profiles Table ---');
  try {
    // First, check the structure of the profiles table
    const { data: profilesStructure, error: structureError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (structureError) {
      console.error('Error querying profiles table:', structureError);
      return;
    }
    
    if (profilesStructure && profilesStructure.length > 0) {
      const columns = Object.keys(profilesStructure[0]);
      console.log('Profiles table columns:', columns);
      
      // Look for phone-related columns
      const phoneColumns = columns.filter(col => 
        col.includes('phone') || col.includes('mobile') || col.includes('contact')
      );
      
      if (phoneColumns.length > 0) {
        console.log('Potential phone columns in profiles:', phoneColumns);
        
        // Get some sample data
        const { data: samples, error: samplesError } = await supabase
          .from('profiles')
          .select(phoneColumns.join(','))
          .limit(5);
        
        if (samplesError) {
          console.error('Error getting samples:', samplesError);
        } else {
          console.log('Sample phone data from profiles:');
          samples.forEach((sample, index) => {
            console.log(`Sample ${index + 1}:`, sample);
          });
        }
      } else {
        console.log('No obvious phone columns found in profiles table');
      }
    }
  } catch (err) {
    console.error('Error in checkProfilesTable:', err);
  }
}

async function checkOrdersTable() {
  console.log('\n--- Checking Orders Table ---');
  try {
    // First, check the structure of the orders table
    const { data: ordersStructure, error: structureError } = await supabase
      .from('orders')
      .select('*')
      .limit(1);
    
    if (structureError) {
      console.error('Error querying orders table:', structureError);
      return;
    }
    
    if (ordersStructure && ordersStructure.length > 0) {
      const columns = Object.keys(ordersStructure[0]);
      console.log('Orders table columns:', columns);
      
      // Look for phone-related columns
      const phoneColumns = columns.filter(col => 
        col.includes('phone') || col.includes('mobile') || col.includes('contact') || 
        col === 'delivery_phone'
      );
      
      if (phoneColumns.length > 0) {
        console.log('Potential phone columns in orders:', phoneColumns);
        
        // Get some sample data
        const { data: samples, error: samplesError } = await supabase
          .from('orders')
          .select(`id, ${phoneColumns.join(',')}`)
          .limit(5);
        
        if (samplesError) {
          console.error('Error getting samples:', samplesError);
        } else {
          console.log('Sample phone data from orders:');
          samples.forEach((sample, index) => {
            console.log(`Sample ${index + 1}:`, sample);
          });
        }
      } else {
        console.log('No obvious phone columns found in orders table');
      }
    }
  } catch (err) {
    console.error('Error in checkOrdersTable:', err);
  }
}

async function checkAddressesTable() {
  console.log('\n--- Checking Addresses Table ---');
  try {
    // First, check the structure of the addresses table
    const { data: addressesStructure, error: structureError } = await supabase
      .from('addresses')
      .select('*')
      .limit(1);
    
    if (structureError) {
      console.error('Error querying addresses table:', structureError);
      return;
    }
    
    if (addressesStructure && addressesStructure.length > 0) {
      const columns = Object.keys(addressesStructure[0]);
      console.log('Addresses table columns:', columns);
      
      // Look for phone-related columns
      const phoneColumns = columns.filter(col => 
        col.includes('phone') || col.includes('mobile') || col.includes('contact')
      );
      
      if (phoneColumns.length > 0) {
        console.log('Potential phone columns in addresses:', phoneColumns);
        
        // Get some sample data
        const { data: samples, error: samplesError } = await supabase
          .from('addresses')
          .select(`id, ${phoneColumns.join(',')}`)
          .limit(5);
        
        if (samplesError) {
          console.error('Error getting samples:', samplesError);
        } else {
          console.log('Sample phone data from addresses:');
          samples.forEach((sample, index) => {
            console.log(`Sample ${index + 1}:`, sample);
          });
        }
      } else {
        console.log('No obvious phone columns found in addresses table');
      }
    }
  } catch (err) {
    console.error('Error in checkAddressesTable:', err);
  }
}

async function checkOrdersWithProfiles() {
  console.log('\n--- Checking Orders with Profiles Join ---');
  try {
    // Join orders with profiles to see the relationship
    const { data, error } = await supabase
      .from('orders')
      .select(`
        id,
        user_id,
        profiles!user_id (
          id, full_name, email, phone, mobile, contact_number
        )
      `)
      .limit(3);
    
    if (error) {
      console.error('Error joining orders with profiles:', error);
      return;
    }
    
    console.log('Sample orders with profile data:');
    data.forEach((item, index) => {
      console.log(`Order ${index + 1}:`, {
        id: item.id,
        user_id: item.user_id,
        profile: item.profiles
      });
    });
  } catch (err) {
    console.error('Error in checkOrdersWithProfiles:', err);
  }
}

// Run all checks
async function runAllChecks() {
  console.log('Starting database checks for phone number storage...');
  
  await checkProfilesTable();
  await checkOrdersTable();
  await checkAddressesTable();
  await checkOrdersWithProfiles();
  
  console.log('\nAll checks completed!');
}

runAllChecks().catch(err => {
  console.error('Error running checks:', err);
});
