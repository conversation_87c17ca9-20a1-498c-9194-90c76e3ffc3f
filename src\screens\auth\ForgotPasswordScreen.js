import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { Button, TextInput } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import * as Yup from 'yup';

import { COLORS, FONT, SIZES, SPACING } from '../../constants/theme';
import { SCREENS } from '../../constants';
import { PLACEHOLDER_IMAGES } from '../../constants/placeholders';
import { resetPassword } from '../../lib/supabase';
import useStore from '../../store/useStore';

// Validation schema
const ForgotPasswordSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email')
    .required('Email is required'),
});

const ForgotPasswordScreen = () => {
  const navigation = useNavigation();
  const { setLoading, setError } = useStore();
  const [isResetting, setIsResetting] = useState(false);

  // Function to handle password reset
  const handleResetPassword = async (values, { setSubmitting, setFieldError }) => {
    try {
      setIsResetting(true);
      setLoading(true);

      // Call Supabase to send password reset email
      const { data, error } = await resetPassword(values.email);

      if (error) {
        if (error.message.includes('email')) {
          setFieldError('email', error.message);
        } else {
          Alert.alert('Reset Error', error.message);
        }
        return;
      }

      // Show success message
      Alert.alert(
        'Password Reset',
        `A password reset link has been sent to ${values.email}`,
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate(SCREENS.LOGIN),
          },
        ]
      );
    } catch (error) {
      console.error('Password reset error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      setError(error.message);
    } finally {
      setIsResetting(false);
      setLoading(false);
      setSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>

          <View style={styles.headerContainer}>
            <Image
              source={{ uri: PLACEHOLDER_IMAGES.LOGO }}
              style={styles.logo}
              resizeMode="contain"
            />
            <Text style={styles.title}>Forgot Password</Text>
            <Text style={styles.subtitle}>
              Enter your email address and we'll send you a link to reset your password
            </Text>
          </View>

          <Formik
            initialValues={{ email: '' }}
            validationSchema={ForgotPasswordSchema}
            onSubmit={handleResetPassword}
          >
            {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
              <View style={styles.formContainer}>
                <TextInput
                  label="Email"
                  value={values.email}
                  onChangeText={handleChange('email')}
                  onBlur={handleBlur('email')}
                  style={styles.input}
                  mode="outlined"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  left={<TextInput.Icon icon="email" />}
                />
                {touched.email && errors.email && (
                  <Text style={styles.errorText}>{errors.email}</Text>
                )}

                <Button
                  mode="contained"
                  style={styles.button}
                  labelStyle={styles.buttonLabel}
                  onPress={handleSubmit}
                  disabled={isResetting}
                  loading={isResetting}
                >
                  {isResetting ? 'Sending Reset Link...' : 'Reset Password'}
                </Button>
              </View>
            )}
          </Formik>

          <View style={styles.footerContainer}>
            <Text style={styles.footerText}>Remember your password?</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate(SCREENS.LOGIN)}
            >
              <Text style={styles.loginText}>Login</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flexGrow: 1,
    paddingHorizontal: SPACING.l,
    paddingTop: SPACING.xl,
    paddingBottom: SPACING.xxl,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: SPACING.l,
  },
  backButtonText: {
    fontSize: SIZES.medium,
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: SPACING.m,
  },
  title: {
    fontSize: SIZES.xxLarge,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.s,
  },
  subtitle: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
    textAlign: 'center',
    marginHorizontal: SPACING.l,
  },
  formContainer: {
    marginBottom: SPACING.xl,
  },
  input: {
    marginBottom: SPACING.s,
    backgroundColor: COLORS.white,
  },
  errorText: {
    color: COLORS.error,
    fontSize: SIZES.small,
    marginBottom: SPACING.s,
    marginLeft: SPACING.s,
  },
  button: {
    marginTop: SPACING.l,
    paddingVertical: SPACING.xs,
    borderRadius: 8,
  },
  buttonLabel: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    paddingVertical: SPACING.xs,
  },
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
    marginRight: SPACING.xs,
  },
  loginText: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
});

export default ForgotPasswordScreen;
