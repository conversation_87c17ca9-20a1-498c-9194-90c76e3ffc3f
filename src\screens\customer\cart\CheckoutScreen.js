import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import {
  IconButton,
  Button,
  Divider,
  TextInput,
  RadioButton,
  Card,
  Title,
  Paragraph,
  ProgressBar,
  List,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import * as Yup from 'yup';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../../constants/theme';
import { SCREENS } from '../../../constants';
import useStore from '../../../store/useStore';

// Validation schema for delivery info
const DeliveryInfoSchema = Yup.object().shape({
  fullName: Yup.string().required('Full name is required'),
  phone: Yup.string().required('Phone number is required'),
  address: Yup.string().required('Address is required'),
  city: Yup.string().required('City is required'),
});

const CheckoutScreen = () => {
  const navigation = useNavigation();
  const { cart, cartTotal, clearCart } = useStore();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [deliveryInfo, setDeliveryInfo] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('airtel_money');
  
  // Handle next step
  const handleNextStep = (values) => {
    if (currentStep === 1) {
      setDeliveryInfo(values);
      setCurrentStep(2);
    } else if (currentStep === 2) {
      setCurrentStep(3);
    }
  };

  // Handle previous step
  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle place order
  const handlePlaceOrder = () => {
    // In a real app, you would make an API call to place the order
    Alert.alert(
      'Order Placed',
      'Your order has been placed successfully!',
      [
        {
          text: 'OK',
          onPress: () => {
            clearCart();
            navigation.navigate(SCREENS.ORDERS);
          },
        },
      ]
    );
  };

  // Render step indicator
  const renderStepIndicator = () => (
    <View style={styles.stepIndicatorContainer}>
      <View style={styles.stepRow}>
        <View style={[styles.stepCircle, currentStep >= 1 && styles.activeStepCircle]}>
          <Text style={[styles.stepNumber, currentStep >= 1 && styles.activeStepNumber]}>1</Text>
        </View>
        <View style={[styles.stepLine, currentStep >= 2 && styles.activeStepLine]} />
        <View style={[styles.stepCircle, currentStep >= 2 && styles.activeStepCircle]}>
          <Text style={[styles.stepNumber, currentStep >= 2 && styles.activeStepNumber]}>2</Text>
        </View>
        <View style={[styles.stepLine, currentStep >= 3 && styles.activeStepLine]} />
        <View style={[styles.stepCircle, currentStep >= 3 && styles.activeStepCircle]}>
          <Text style={[styles.stepNumber, currentStep >= 3 && styles.activeStepNumber]}>3</Text>
        </View>
      </View>
      <View style={styles.stepLabelRow}>
        <Text style={[styles.stepLabel, currentStep >= 1 && styles.activeStepLabel]}>Delivery</Text>
        <Text style={[styles.stepLabel, currentStep >= 2 && styles.activeStepLabel]}>Payment</Text>
        <Text style={[styles.stepLabel, currentStep >= 3 && styles.activeStepLabel]}>Confirm</Text>
      </View>
    </View>
  );

  // Render delivery info step
  const renderDeliveryInfoStep = () => (
    <Formik
      initialValues={
        deliveryInfo || {
          fullName: '',
          phone: '',
          address: '',
          city: '',
          notes: '',
        }
      }
      validationSchema={DeliveryInfoSchema}
      onSubmit={handleNextStep}
    >
      {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
        <View style={styles.stepContainer}>
          <Text style={styles.stepTitle}>Delivery Information</Text>
          
          <TextInput
            label="Full Name"
            value={values.fullName}
            onChangeText={handleChange('fullName')}
            onBlur={handleBlur('fullName')}
            style={styles.input}
            error={touched.fullName && errors.fullName}
          />
          {touched.fullName && errors.fullName && (
            <Text style={styles.errorText}>{errors.fullName}</Text>
          )}
          
          <TextInput
            label="Phone Number"
            value={values.phone}
            onChangeText={handleChange('phone')}
            onBlur={handleBlur('phone')}
            style={styles.input}
            keyboardType="phone-pad"
            error={touched.phone && errors.phone}
          />
          {touched.phone && errors.phone && (
            <Text style={styles.errorText}>{errors.phone}</Text>
          )}
          
          <TextInput
            label="Delivery Address"
            value={values.address}
            onChangeText={handleChange('address')}
            onBlur={handleBlur('address')}
            style={styles.input}
            error={touched.address && errors.address}
          />
          {touched.address && errors.address && (
            <Text style={styles.errorText}>{errors.address}</Text>
          )}
          
          <TextInput
            label="City"
            value={values.city}
            onChangeText={handleChange('city')}
            onBlur={handleBlur('city')}
            style={styles.input}
            error={touched.city && errors.city}
          />
          {touched.city && errors.city && (
            <Text style={styles.errorText}>{errors.city}</Text>
          )}
          
          <TextInput
            label="Additional Notes (Optional)"
            value={values.notes}
            onChangeText={handleChange('notes')}
            onBlur={handleBlur('notes')}
            style={styles.input}
            multiline
            numberOfLines={3}
          />
          
          <View style={styles.buttonContainer}>
            <Button
              mode="outlined"
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              Back to Cart
            </Button>
            <Button
              mode="contained"
              style={styles.nextButton}
              onPress={handleSubmit}
            >
              Next
            </Button>
          </View>
        </View>
      )}
    </Formik>
  );

  // Render payment method step
  const renderPaymentMethodStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Payment Method</Text>
      
      <RadioButton.Group
        onValueChange={(value) => setPaymentMethod(value)}
        value={paymentMethod}
      >
        <Card style={[styles.paymentCard, paymentMethod === 'airtel_money' && styles.selectedPaymentCard]}>
          <TouchableOpacity
            style={styles.paymentOption}
            onPress={() => setPaymentMethod('airtel_money')}
          >
            <View style={styles.paymentIconContainer}>
              <MaterialCommunityIcons name="cellphone" size={24} color={COLORS.error} />
            </View>
            <View style={styles.paymentDetails}>
              <Text style={styles.paymentTitle}>Airtel Money</Text>
              <Text style={styles.paymentDescription}>Pay using your Airtel Money account</Text>
            </View>
            <RadioButton value="airtel_money" />
          </TouchableOpacity>
        </Card>
        
        <Card style={[styles.paymentCard, paymentMethod === 'mpamba' && styles.selectedPaymentCard]}>
          <TouchableOpacity
            style={styles.paymentOption}
            onPress={() => setPaymentMethod('mpamba')}
          >
            <View style={styles.paymentIconContainer}>
              <MaterialCommunityIcons name="cellphone" size={24} color={COLORS.success} />
            </View>
            <View style={styles.paymentDetails}>
              <Text style={styles.paymentTitle}>Mpamba</Text>
              <Text style={styles.paymentDescription}>Pay using your Mpamba account</Text>
            </View>
            <RadioButton value="mpamba" />
          </TouchableOpacity>
        </Card>
        
        <Card style={[styles.paymentCard, paymentMethod === 'cash' && styles.selectedPaymentCard]}>
          <TouchableOpacity
            style={styles.paymentOption}
            onPress={() => setPaymentMethod('cash')}
          >
            <View style={styles.paymentIconContainer}>
              <MaterialCommunityIcons name="cash" size={24} color={COLORS.primary} />
            </View>
            <View style={styles.paymentDetails}>
              <Text style={styles.paymentTitle}>Cash on Delivery</Text>
              <Text style={styles.paymentDescription}>Pay when you receive your order</Text>
            </View>
            <RadioButton value="cash" />
          </TouchableOpacity>
        </Card>
      </RadioButton.Group>
      
      <View style={styles.buttonContainer}>
        <Button
          mode="outlined"
          style={styles.backButton}
          onPress={handlePreviousStep}
        >
          Back
        </Button>
        <Button
          mode="contained"
          style={styles.nextButton}
          onPress={() => handleNextStep()}
        >
          Next
        </Button>
      </View>
    </View>
  );

  // Render order confirmation step
  const renderOrderConfirmationStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Order Confirmation</Text>
      
      <Card style={styles.summaryCard}>
        <Card.Content>
          <Title style={styles.summaryTitle}>Delivery Information</Title>
          <List.Item
            title={deliveryInfo.fullName}
            description={deliveryInfo.phone}
            left={props => <List.Icon {...props} icon="account" />}
          />
          <List.Item
            title={deliveryInfo.address}
            description={deliveryInfo.city}
            left={props => <List.Icon {...props} icon="map-marker" />}
          />
          {deliveryInfo.notes && (
            <List.Item
              title="Additional Notes"
              description={deliveryInfo.notes}
              left={props => <List.Icon {...props} icon="note-text" />}
            />
          )}
          
          <Divider style={styles.divider} />
          
          <Title style={styles.summaryTitle}>Payment Method</Title>
          <List.Item
            title={
              paymentMethod === 'airtel_money' ? 'Airtel Money' :
              paymentMethod === 'mpamba' ? 'Mpamba' : 'Cash on Delivery'
            }
            left={props => <List.Icon {...props} icon={
              paymentMethod === 'cash' ? 'cash' : 'cellphone'
            } />}
          />
          
          <Divider style={styles.divider} />
          
          <Title style={styles.summaryTitle}>Order Summary</Title>
          {cart.map((item) => (
            <List.Item
              key={item.id}
              title={item.name}
              description={`${item.quantity} x $${item.price.toFixed(2)}`}
              right={() => <Text style={styles.itemTotal}>${(item.price * item.quantity).toFixed(2)}</Text>}
            />
          ))}
          
          <Divider style={styles.divider} />
          
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Subtotal</Text>
            <Text style={styles.totalValue}>${cartTotal.toFixed(2)}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Delivery Fee</Text>
            <Text style={styles.totalValue}>$0.00</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={[styles.totalLabel, styles.grandTotalLabel]}>Total</Text>
            <Text style={[styles.totalValue, styles.grandTotalValue]}>${cartTotal.toFixed(2)}</Text>
          </View>
        </Card.Content>
      </Card>
      
      <View style={styles.buttonContainer}>
        <Button
          mode="outlined"
          style={styles.backButton}
          onPress={handlePreviousStep}
        >
          Back
        </Button>
        <Button
          mode="contained"
          style={styles.placeOrderButton}
          onPress={handlePlaceOrder}
        >
          Place Order
        </Button>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => {
            if (currentStep > 1) {
              handlePreviousStep();
            } else {
              navigation.goBack();
            }
          }}
        />
        <Text style={styles.headerTitle}>Checkout</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Checkout Steps */}
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {currentStep === 1 && renderDeliveryInfoStep()}
        {currentStep === 2 && renderPaymentMethodStep()}
        {currentStep === 3 && renderOrderConfirmationStep()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.s,
    paddingVertical: SPACING.s,
    ...SHADOWS.small,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: SPACING.xl,
  },
  stepIndicatorContainer: {
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
    ...SHADOWS.small,
  },
  stepRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  stepCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: COLORS.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeStepCircle: {
    backgroundColor: COLORS.primary,
  },
  stepNumber: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.gray,
  },
  activeStepNumber: {
    color: COLORS.white,
  },
  stepLine: {
    flex: 1,
    height: 2,
    backgroundColor: COLORS.lightGray,
    marginHorizontal: SPACING.s,
  },
  activeStepLine: {
    backgroundColor: COLORS.primary,
  },
  stepLabelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.xs,
  },
  stepLabel: {
    fontSize: SIZES.small,
    color: COLORS.gray,
    textAlign: 'center',
    width: '33%',
  },
  activeStepLabel: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  stepContainer: {
    backgroundColor: COLORS.white,
    margin: SPACING.m,
    padding: SPACING.m,
    borderRadius: 8,
    ...SHADOWS.small,
  },
  stepTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.m,
  },
  input: {
    marginBottom: SPACING.s,
    backgroundColor: COLORS.white,
  },
  errorText: {
    color: COLORS.error,
    fontSize: SIZES.small,
    marginBottom: SPACING.s,
    marginLeft: SPACING.s,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.m,
  },
  backButton: {
    flex: 1,
    marginRight: SPACING.s,
    borderColor: COLORS.primary,
  },
  nextButton: {
    flex: 2,
    backgroundColor: COLORS.primary,
  },
  placeOrderButton: {
    flex: 2,
    backgroundColor: COLORS.success,
  },
  paymentCard: {
    marginBottom: SPACING.s,
    elevation: 1,
  },
  selectedPaymentCard: {
    borderColor: COLORS.primary,
    borderWidth: 1,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.m,
  },
  paymentIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.m,
  },
  paymentDetails: {
    flex: 1,
  },
  paymentTitle: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  paymentDescription: {
    fontSize: SIZES.small,
    color: COLORS.gray,
  },
  summaryCard: {
    marginBottom: SPACING.m,
  },
  summaryTitle: {
    fontSize: SIZES.medium,
    marginBottom: SPACING.xs,
  },
  divider: {
    marginVertical: SPACING.m,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: SPACING.xs,
  },
  totalLabel: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
  totalValue: {
    fontSize: SIZES.medium,
    color: COLORS.text,
  },
  grandTotalLabel: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  grandTotalValue: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  itemTotal: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
});

export default CheckoutScreen;
