import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import {
  IconButton,
  Chip,
  Divider,
  Card,
  Title,
  Paragraph,
  Badge,
  List,
  Avatar,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../../constants/theme';
import { SCREENS } from '../../../constants';
import useStore from '../../../store/useStore';

const OrdersScreen = () => {
  const navigation = useNavigation();
  const { orders, filteredOrders, filterOrders } = useStore();
  
  const [selectedStatus, setSelectedStatus] = useState(null);

  // Order statuses
  const orderStatuses = [
    { id: 'all', label: 'All', color: COLORS.primary },
    { id: 'pending', label: 'Pending', color: COLORS.warning },
    { id: 'processing', label: 'Processing', color: COLORS.info },
    { id: 'shipped', label: 'Shipped', color: COLORS.secondary },
    { id: 'delivered', label: 'Delivered', color: COLORS.success },
    { id: 'cancelled', label: 'Cancelled', color: COLORS.error },
  ];

  // Handle status filter
  const handleStatusFilter = (status) => {
    if (status === 'all') {
      setSelectedStatus('all');
      filterOrders(null);
    } else {
      setSelectedStatus(status);
      filterOrders(status);
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    const statusObj = orderStatuses.find(s => s.id === status);
    return statusObj ? statusObj.color : COLORS.gray;
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Render order item
  const renderOrderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.orderCard}
      onPress={() => navigation.navigate(SCREENS.ORDER_DETAILS, { orderId: item.id })}
    >
      <View style={styles.orderHeader}>
        <View>
          <Text style={styles.orderId}>Order #{item.id}</Text>
          <Text style={styles.orderDate}>{formatDate(item.createdAt)}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.orderItems}>
        {item.items.map((orderItem, index) => (
          <View key={index} style={styles.orderItem}>
            <MaterialCommunityIcons
              name="circle-small"
              size={20}
              color={COLORS.gray}
            />
            <Text style={styles.itemName} numberOfLines={1}>
              {orderItem.product.name} x {orderItem.quantity}
            </Text>
          </View>
        ))}
      </View>

      <View style={styles.orderFooter}>
        <Text style={styles.totalLabel}>Total:</Text>
        <Text style={styles.totalValue}>${item.total.toFixed(2)}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Orders</Text>
      </View>

      {/* Status Filters */}
      <View style={styles.filtersContainer}>
        <FlatList
          data={orderStatuses}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <Chip
              selected={selectedStatus === item.id}
              onPress={() => handleStatusFilter(item.id)}
              style={[
                styles.filterChip,
                selectedStatus === item.id && { backgroundColor: item.color }
              ]}
              textStyle={[
                styles.filterChipText,
                selectedStatus === item.id && styles.selectedFilterChipText
              ]}
            >
              {item.label}
            </Chip>
          )}
          contentContainerStyle={styles.filtersList}
        />
      </View>

      {/* Orders List */}
      <FlatList
        data={selectedStatus === 'all' || !selectedStatus ? orders : filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.ordersList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons name="clipboard-text-off" size={64} color={COLORS.gray} />
            <Text style={styles.emptyText}>No orders found</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  filtersContainer: {
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.s,
    ...SHADOWS.small,
  },
  filtersList: {
    paddingHorizontal: SPACING.m,
  },
  filterChip: {
    marginRight: SPACING.s,
    backgroundColor: COLORS.lightGray,
  },
  filterChipText: {
    color: COLORS.text,
  },
  selectedFilterChipText: {
    color: COLORS.white,
  },
  ordersList: {
    padding: SPACING.m,
  },
  orderCard: {
    backgroundColor: COLORS.white,
    borderRadius: 8,
    padding: SPACING.m,
    marginBottom: SPACING.m,
    ...SHADOWS.small,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  orderId: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  orderDate: {
    fontSize: SIZES.small,
    color: COLORS.gray,
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: SPACING.s,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: COLORS.white,
    fontSize: SIZES.xSmall,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  divider: {
    marginVertical: SPACING.s,
  },
  orderItems: {
    marginBottom: SPACING.s,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  itemName: {
    fontSize: SIZES.small,
    color: COLORS.text,
    flex: 1,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
    marginRight: SPACING.s,
  },
  totalValue: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.m,
    fontSize: SIZES.medium,
    color: COLORS.gray,
  },
});

export default OrdersScreen;
