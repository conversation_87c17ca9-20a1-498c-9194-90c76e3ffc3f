-- Insert initial categories
INSERT INTO categories (name, description, icon, image_url)
VALUES 
('Vegetables', 'Fresh locally grown vegetables', 'leaf', 'https://images.unsplash.com/photo-1566385101042-1a0aa0c1268c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80'),
('Fruits', 'Sweet and juicy fruits', 'apple', 'https://images.unsplash.com/photo-1619566636858-adf3ef46400b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'),
('Dairy', 'Fresh milk and dairy products', 'glass-milk', 'https://images.unsplash.com/photo-1628088062854-d1870b4553da?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'),
('Grains', 'Wholesome grains and cereals', 'grain', 'https://images.unsplash.com/photo-1574323347407-f5e1c5a1ec21?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'),
('Meat', 'Quality meat products', 'food-steak', 'https://images.unsplash.com/photo-1607623814075-e51df1bdc82f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'),
('Herbs', 'Fresh aromatic herbs', 'grass', 'https://images.unsplash.com/photo-1600508537197-e58ced11e0a4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');

-- Note: To create an admin user, first create a user through Supabase Auth,
-- then update their profile to set is_admin to TRUE.
-- Example (replace 'USER_ID' with the actual user ID):
-- UPDATE profiles SET is_admin = TRUE WHERE id = 'USER_ID';

-- Insert sample farmer
INSERT INTO farmers (business_name, description, location, contact_email, contact_phone, logo_url, banner_url, is_verified)
VALUES 
('Green Valley Farms', 'Family-owned farm specializing in organic vegetables and fruits', 'Kampala East', '<EMAIL>', '+************', 'https://images.unsplash.com/photo-1560493676-04071c5f467b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80', 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80', TRUE);

-- Get the farmer ID for use in product insertion
DO $$
DECLARE
    farmer_id UUID;
    vegetable_category_id UUID;
    fruit_category_id UUID;
BEGIN
    -- Get the farmer ID
    SELECT id INTO farmer_id FROM farmers WHERE business_name = 'Green Valley Farms';
    
    -- Get category IDs
    SELECT id INTO vegetable_category_id FROM categories WHERE name = 'Vegetables';
    SELECT id INTO fruit_category_id FROM categories WHERE name = 'Fruits';
    
    -- Insert sample products
    INSERT INTO products (name, description, price, unit, stock, category_id, farmer_id, image_url, is_organic, is_featured)
    VALUES 
    ('Fresh Tomatoes', 'Locally grown fresh tomatoes from sustainable farms. Rich in flavor and perfect for salads, sauces, and cooking.', 2.99, 'kg', 50, vegetable_category_id, farmer_id, 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80', TRUE, TRUE),
    
    ('Organic Carrots', 'Sweet and crunchy carrots grown without pesticides. Great for snacking, cooking, or juicing.', 1.99, 'kg', 75, vegetable_category_id, farmer_id, 'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80', TRUE, FALSE),
    
    ('Sweet Bananas', 'Ripe and sweet bananas, perfect for snacking or baking.', 3.49, 'bunch', 40, fruit_category_id, farmer_id, 'https://images.unsplash.com/photo-1603833665858-e61d17a86224?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80', TRUE, TRUE),
    
    ('Fresh Spinach', 'Nutrient-rich spinach leaves, perfect for salads and cooking.', 2.49, 'bunch', 30, vegetable_category_id, farmer_id, 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1480&q=80', TRUE, FALSE);
END $$;
