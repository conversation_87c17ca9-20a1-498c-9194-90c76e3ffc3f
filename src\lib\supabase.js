import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://saxcjcqohvabropicrkk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNheGNqY3FvaHZhYnJvcGljcmtrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2MTYxNzUsImV4cCI6MjA2MjE5MjE3NX0.ONVChVWSjr4U185BjJgxK8o6JhIxZ2Z46RAL6xxvQVc';

// Create a single supabase client for interacting with your database
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Auth functions
export const signUp = async (email, password, userData) => {
  try {
    // Register the user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: userData.fullName,
          phone: userData.phone,
        },
        // For React Native, we don't need to specify emailRedirectTo
        // The verification will happen in the email client
      },
    });

    if (authError) throw authError;

    // If successful, the database trigger will create a profile automatically
    // We don't need to manually create a profile here
    if (authData.user) {
      console.log('User created successfully:', authData.user.id);
      // The database trigger handle_new_user() will create the profile

      // Try to show an alert directly from here as a fallback
      try {
        const { Alert } = require('react-native');
        Alert.alert(
          'Registration Successful (from supabase.js)',
          'Your account has been created successfully! Please check your email and verify your account before logging in.'
        );
      } catch (alertError) {
        console.log('Could not show direct alert:', alertError);
      }
    }

    return { data: authData, error: null };
  } catch (error) {
    console.error('Error signing up:', error.message);
    return { data: null, error };
  }
};

export const signIn = async (email, password) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error signing in:', error.message);
    return { data: null, error };
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error signing out:', error.message);
    return { error };
  }
};

export const resetPassword = async (email) => {
  try {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: 'zakumunda://reset-password',
    });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error resetting password:', error.message);
    return { data: null, error };
  }
};

export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) throw error;

    if (user) {
      // Get additional profile data from the profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        // PGRST116 is the error code for "no rows returned"
        throw profileError;
      }

      // Combine auth user with profile data
      const userData = {
        id: user.id,
        email: user.email,
        ...user.user_metadata,
        ...profileData,
      };

      return { user: userData, error: null };
    }

    return { user: null, error: null };
  } catch (error) {
    console.error('Error getting current user:', error.message);
    return { user: null, error };
  }
};

// Product functions
export const getProducts = async (options = {}) => {
  try {
    let query = supabase.from('products').select(`
      *,
      categories(*),
      farmers(*)
    `);

    // Apply filters if provided
    if (options.category_id) {
      query = query.eq('category_id', options.category_id);
    }

    if (options.is_featured) {
      query = query.eq('is_featured', options.is_featured);
    }

    if (options.is_organic) {
      query = query.eq('is_organic', options.is_organic);
    }

    if (options.search) {
      query = query.ilike('name', `%${options.search}%`);
    }

    // Apply sorting
    if (options.sort_by) {
      const order = options.sort_order || 'asc';
      query = query.order(options.sort_by, { ascending: order === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error } = await query;

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching products:', error.message);
    return { data: null, error };
  }
};

export const getProductById = async (id) => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories(*),
        farmers(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error fetching product with ID ${id}:`, error.message);
    return { data: null, error };
  }
};

// Order functions
export const createOrder = async (orderData) => {
  try {
    // First, create the order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert([
        {
          user_id: orderData.user_id,
          address_id: orderData.address_id,
          total_amount: orderData.total_amount,
          payment_method: orderData.payment_method,
          notes: orderData.notes
        }
      ])
      .select()
      .single();

    if (orderError) throw orderError;

    // Then, create the order items
    const orderItems = orderData.items.map(item => ({
      order_id: order.id,
      product_id: item.product_id,
      quantity: item.quantity,
      price_per_unit: item.price,
      total_price: item.price * item.quantity
    }));

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems);

    if (itemsError) throw itemsError;

    return { data: order, error: null };
  } catch (error) {
    console.error('Error creating order:', error.message);
    return { data: null, error };
  }
};

export const getUserOrders = async (userId) => {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items(
          *,
          products(*)
        ),
        addresses(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error fetching orders for user ${userId}:`, error.message);
    return { data: null, error };
  }
};

// Address functions
export const getUserAddresses = async (userId) => {
  try {
    const { data, error } = await supabase
      .from('addresses')
      .select('*')
      .eq('user_id', userId)
      .order('is_default', { ascending: false });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error fetching addresses for user ${userId}:`, error.message);
    return { data: null, error };
  }
};

export const addUserAddress = async (addressData) => {
  try {
    const { data, error } = await supabase
      .from('addresses')
      .insert([addressData])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error adding address:', error.message);
    return { data: null, error };
  }
};
