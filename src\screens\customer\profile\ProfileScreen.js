import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  Alert,
  Switch,
} from 'react-native';
import {
  Avatar,
  Button,
  Divider,
  List,
  IconButton,
  Dialog,
  Portal,
  TextInput,
  RadioButton,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';

import { COLORS, FONT, SIZES, SPACING, SHADOWS } from '../../../constants/theme';
import { SCREENS } from '../../../constants';
import useStore from '../../../store/useStore';

const ProfileScreen = () => {
  const navigation = useNavigation();
  const { user, logout } = useStore();

  const [profileImage, setProfileImage] = useState('https://randomuser.me/api/portraits/men/32.jpg');
  const [editProfileVisible, setEditProfileVisible] = useState(false);
  const [editAddressVisible, setEditAddressVisible] = useState(false);
  const [themeDialogVisible, setThemeDialogVisible] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [selectedTheme, setSelectedTheme] = useState('light');

  // Sample user data
  const userData = {
    name: user?.name || 'John Doe',
    email: user?.email || '<EMAIL>',
    phone: '1234567890',
    addresses: [
      {
        id: '1',
        type: 'Home',
        address: '123 Main St',
        city: 'Kampala',
        isDefault: true,
      },
      {
        id: '2',
        type: 'Work',
        address: '456 Office Blvd',
        city: 'Kampala',
        isDefault: false,
      },
    ],
  };

  // Handle profile image pick
  const handlePickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Sorry, we need camera roll permissions to upload images.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      setProfileImage(result.assets[0].uri);
    }
  };

  // Handle logout
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          onPress: async () => {
            try {
              // Show loading indicator or disable buttons if needed

              // Call the logout function which now handles Supabase signOut
              await logout();

              // Navigate to welcome screen after successful logout
              navigation.reset({
                index: 0,
                routes: [{ name: SCREENS.WELCOME }],
              });
            } catch (error) {
              console.error('Error during logout:', error);
              Alert.alert('Logout Error', 'There was a problem logging out. Please try again.');
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  // Render edit profile dialog
  const renderEditProfileDialog = () => (
    <Portal>
      <Dialog
        visible={editProfileVisible}
        onDismiss={() => setEditProfileVisible(false)}
        style={styles.dialog}
      >
        <Dialog.Title>Edit Profile</Dialog.Title>
        <Dialog.Content>
          <TextInput
            label="Full Name"
            value={userData.name}
            style={styles.dialogInput}
          />
          <TextInput
            label="Email"
            value={userData.email}
            style={styles.dialogInput}
            keyboardType="email-address"
          />
          <TextInput
            label="Phone"
            value={userData.phone}
            style={styles.dialogInput}
            keyboardType="phone-pad"
          />
        </Dialog.Content>
        <Dialog.Actions>
          <Button onPress={() => setEditProfileVisible(false)}>Cancel</Button>
          <Button
            onPress={() => {
              // In a real app, you would update the user profile here
              setEditProfileVisible(false);
              Alert.alert('Success', 'Profile updated successfully');
            }}
          >
            Save
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );

  // Render edit address dialog
  const renderEditAddressDialog = () => (
    <Portal>
      <Dialog
        visible={editAddressVisible}
        onDismiss={() => setEditAddressVisible(false)}
        style={styles.dialog}
      >
        <Dialog.Title>Edit Address</Dialog.Title>
        <Dialog.Content>
          <TextInput
            label="Address Type"
            value="Home"
            style={styles.dialogInput}
          />
          <TextInput
            label="Address"
            value="123 Main St"
            style={styles.dialogInput}
          />
          <TextInput
            label="City"
            value="Kampala"
            style={styles.dialogInput}
          />
          <View style={styles.defaultAddressContainer}>
            <Text>Set as default address</Text>
            <Switch value={true} />
          </View>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onPress={() => setEditAddressVisible(false)}>Cancel</Button>
          <Button
            onPress={() => {
              // In a real app, you would update the address here
              setEditAddressVisible(false);
              Alert.alert('Success', 'Address updated successfully');
            }}
          >
            Save
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );

  // Render theme dialog
  const renderThemeDialog = () => (
    <Portal>
      <Dialog
        visible={themeDialogVisible}
        onDismiss={() => setThemeDialogVisible(false)}
        style={styles.dialog}
      >
        <Dialog.Title>Theme</Dialog.Title>
        <Dialog.Content>
          <RadioButton.Group
            onValueChange={(value) => setSelectedTheme(value)}
            value={selectedTheme}
          >
            <RadioButton.Item label="Light" value="light" />
            <RadioButton.Item label="Dark" value="dark" />
            <RadioButton.Item label="System Default" value="system" />
          </RadioButton.Group>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onPress={() => setThemeDialogVisible(false)}>Cancel</Button>
          <Button
            onPress={() => {
              // In a real app, you would update the theme here
              setThemeDialogVisible(false);
              Alert.alert('Success', 'Theme updated successfully');
            }}
          >
            Apply
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.primary} />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Profile</Text>
        <IconButton
          icon="logout"
          color={COLORS.white}
          size={24}
          onPress={handleLogout}
        />
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <View style={styles.profileSection}>
          <TouchableOpacity
            style={styles.profileImageContainer}
            onPress={handlePickImage}
          >
            <Avatar.Image
              size={100}
              source={{ uri: profileImage }}
              style={styles.profileImage}
            />
            <View style={styles.editImageButton}>
              <MaterialCommunityIcons name="camera" size={20} color={COLORS.white} />
            </View>
          </TouchableOpacity>
          <Text style={styles.userName}>{userData.name}</Text>
          <Text style={styles.userEmail}>{userData.email}</Text>
          <Button
            mode="outlined"
            style={styles.editProfileButton}
            onPress={() => setEditProfileVisible(true)}
          >
            Edit Profile
          </Button>
        </View>

        <Divider style={styles.divider} />

        {/* My Orders */}
        <List.Section>
          <List.Subheader style={styles.sectionHeader}>My Orders</List.Subheader>
          <List.Item
            title="Order History"
            left={props => <List.Icon {...props} icon="clipboard-list" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => navigation.navigate(SCREENS.ORDERS)}
          />
          <List.Item
            title="Pending Orders"
            left={props => <List.Icon {...props} icon="clock-outline" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => navigation.navigate(SCREENS.ORDERS)}
          />
        </List.Section>

        <Divider style={styles.divider} />

        {/* Address Book */}
        <List.Section>
          <List.Subheader style={styles.sectionHeader}>Address Book</List.Subheader>
          {userData.addresses.map((address) => (
            <List.Item
              key={address.id}
              title={`${address.type} ${address.isDefault ? '(Default)' : ''}`}
              description={`${address.address}, ${address.city}`}
              left={props => <List.Icon {...props} icon="map-marker" />}
              right={() => (
                <IconButton
                  icon="pencil"
                  size={20}
                  onPress={() => setEditAddressVisible(true)}
                />
              )}
            />
          ))}
          <Button
            mode="text"
            style={styles.addButton}
            icon="plus"
            onPress={() => setEditAddressVisible(true)}
          >
            Add New Address
          </Button>
        </List.Section>

        <Divider style={styles.divider} />

        {/* Settings */}
        <List.Section>
          <List.Subheader style={styles.sectionHeader}>Settings</List.Subheader>
          <List.Item
            title="Theme"
            description={selectedTheme === 'light' ? 'Light' : selectedTheme === 'dark' ? 'Dark' : 'System Default'}
            left={props => <List.Icon {...props} icon="theme-light-dark" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => setThemeDialogVisible(true)}
          />
          <List.Item
            title="Notifications"
            left={props => <List.Icon {...props} icon="bell" />}
            right={() => (
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                color={COLORS.primary}
              />
            )}
          />
          <List.Item
            title="Language"
            description="English"
            left={props => <List.Icon {...props} icon="translate" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Language', 'This feature is not implemented in the demo.')}
          />
        </List.Section>

        <Divider style={styles.divider} />

        {/* Support */}
        <List.Section>
          <List.Subheader style={styles.sectionHeader}>Support</List.Subheader>
          <List.Item
            title="Help Center"
            left={props => <List.Icon {...props} icon="help-circle" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Help Center', 'This feature is not implemented in the demo.')}
          />
          <List.Item
            title="Contact Us"
            left={props => <List.Icon {...props} icon="email" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Contact Us', 'This feature is not implemented in the demo.')}
          />
          <List.Item
            title="About"
            left={props => <List.Icon {...props} icon="information" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('About', 'Zakumunda Farm Produce Marketplace\nVersion 1.0.0')}
          />
        </List.Section>

        {/* Logout Button */}
        <Button
          mode="contained"
          style={styles.logoutButton}
          icon="logout"
          onPress={handleLogout}
        >
          Logout
        </Button>

        <View style={styles.bottomSpacer} />
      </ScrollView>

      {/* Dialogs */}
      {renderEditProfileDialog()}
      {renderEditAddressDialog()}
      {renderThemeDialog()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.m,
    paddingHorizontal: SPACING.l,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  profileSection: {
    alignItems: 'center',
    padding: SPACING.l,
    backgroundColor: COLORS.white,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: SPACING.m,
  },
  profileImage: {
    backgroundColor: COLORS.lightGray,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: COLORS.primary,
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.small,
  },
  userName: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  userEmail: {
    fontSize: SIZES.medium,
    color: COLORS.gray,
    marginBottom: SPACING.m,
  },
  editProfileButton: {
    borderColor: COLORS.primary,
  },
  divider: {
    height: 8,
    backgroundColor: COLORS.lightGray,
  },
  sectionHeader: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  addButton: {
    marginTop: SPACING.xs,
  },
  logoutButton: {
    margin: SPACING.l,
    backgroundColor: COLORS.error,
  },
  bottomSpacer: {
    height: 40,
  },
  dialog: {
    backgroundColor: COLORS.white,
  },
  dialogInput: {
    marginBottom: SPACING.s,
    backgroundColor: COLORS.white,
  },
  defaultAddressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: SPACING.s,
  },
});

export default ProfileScreen;
