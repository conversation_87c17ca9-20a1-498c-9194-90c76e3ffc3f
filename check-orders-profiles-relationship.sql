-- Check the relationship between orders and profiles

-- Get a sample of orders data
SELECT 
    *
FROM 
    orders
LIMIT 3;

-- Check if orders has a user_id column
SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' AND table_name = 'orders' AND column_name = 'user_id'
) as has_user_id_column;

-- Check if profiles has a phone column
SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'phone'
) as has_phone_column;

-- Join orders with profiles to see the relationship
SELECT 
    o.id as order_id,
    o.user_id,
    p.id as profile_id,
    p.full_name,
    p.email
FROM 
    orders o
LEFT JOIN 
    profiles p ON o.user_id = p.id
LIMIT 5;

-- Check if we can access the phone field through the relationship
SELECT 
    o.id as order_id,
    o.user_id,
    p.id as profile_id,
    p.full_name,
    p.email,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'phone'
    ) THEN p.phone ELSE 'column_not_found' END as profile_phone
FROM 
    orders o
LEFT JOIN 
    profiles p ON o.user_id = p.id
LIMIT 5;

-- Check if there are any orders with delivery_phone
SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' AND table_name = 'orders' AND column_name = 'delivery_phone'
) as has_delivery_phone_column;

-- If delivery_phone exists, check for orders with that field populated
WITH check_column AS (
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'orders' AND column_name = 'delivery_phone'
    ) as has_delivery_phone
)
SELECT 
    o.id,
    o.user_id,
    CASE WHEN c.has_delivery_phone THEN o.delivery_phone ELSE 'column_not_found' END as delivery_phone
FROM 
    orders o
CROSS JOIN
    check_column c
WHERE 
    c.has_delivery_phone AND o.delivery_phone IS NOT NULL
LIMIT 5;
