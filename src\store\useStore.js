import { create } from 'zustand';
import { SAMPLE_PRODUCTS } from '../constants';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Sample orders for development
const SAMPLE_ORDERS = [
  {
    id: '1',
    customer: {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '1234567890',
      address: '123 Main St, Kampala',
    },
    items: [
      {
        product: SAMPLE_PRODUCTS[0],
        quantity: 2,
        price: SAMPLE_PRODUCTS[0].price,
      },
      {
        product: SAMPLE_PRODUCTS[1],
        quantity: 1,
        price: SAMPLE_PRODUCTS[1].price,
      },
    ],
    total: SAMPLE_PRODUCTS[0].price * 2 + SAMPLE_PRODUCTS[1].price,
    status: 'delivered',
    paymentMethod: 'cash',
    createdAt: new Date('2023-05-01').toISOString(),
    updatedAt: new Date('2023-05-02').toISOString(),
  },
  {
    id: '2',
    customer: {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '0987654321',
      address: '456 Oak St, Entebbe',
    },
    items: [
      {
        product: SAMPLE_PRODUCTS[2],
        quantity: 3,
        price: SAMPLE_PRODUCTS[2].price,
      },
    ],
    total: SAMPLE_PRODUCTS[2].price * 3,
    status: 'processing',
    paymentMethod: 'mobile_money',
    createdAt: new Date('2023-05-03').toISOString(),
    updatedAt: new Date('2023-05-03').toISOString(),
  },
  {
    id: '3',
    customer: {
      id: '3',
      name: 'Robert Johnson',
      email: '<EMAIL>',
      phone: '5556667777',
      address: '789 Pine St, Jinja',
    },
    items: [
      {
        product: SAMPLE_PRODUCTS[0],
        quantity: 1,
        price: SAMPLE_PRODUCTS[0].price,
      },
      {
        product: SAMPLE_PRODUCTS[1],
        quantity: 2,
        price: SAMPLE_PRODUCTS[1].price,
      },
      {
        product: SAMPLE_PRODUCTS[2],
        quantity: 1,
        price: SAMPLE_PRODUCTS[2].price,
      },
    ],
    total: SAMPLE_PRODUCTS[0].price + SAMPLE_PRODUCTS[1].price * 2 + SAMPLE_PRODUCTS[2].price,
    status: 'pending',
    paymentMethod: 'card',
    createdAt: new Date('2023-05-04').toISOString(),
    updatedAt: new Date('2023-05-04').toISOString(),
  },
];

// Sample notifications for development
const SAMPLE_NOTIFICATIONS = [
  {
    id: '1',
    title: 'New Order',
    message: 'You have received a new order from John Doe',
    isRead: false,
    createdAt: new Date('2023-05-04T10:30:00').toISOString(),
  },
  {
    id: '2',
    title: 'Low Stock Alert',
    message: 'Tomatoes are running low on stock (5 units left)',
    isRead: true,
    createdAt: new Date('2023-05-03T14:45:00').toISOString(),
  },
  {
    id: '3',
    title: 'Payment Received',
    message: 'Payment of $15.96 received from Jane Smith',
    isRead: false,
    createdAt: new Date('2023-05-02T09:15:00').toISOString(),
  },
];

// Constants for AsyncStorage keys
const CART_STORAGE_KEY = '@zakumunda_cart';
const CART_TOTAL_STORAGE_KEY = '@zakumunda_cart_total';

// Helper functions for cart persistence
const saveCartToStorage = async (cart, total) => {
  try {
    await AsyncStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));
    await AsyncStorage.setItem(CART_TOTAL_STORAGE_KEY, JSON.stringify(total));
    console.log('Cart saved to storage:', cart.length, 'items, total:', total);
  } catch (error) {
    console.error('Error saving cart to storage:', error);
  }
};

const loadCartFromStorage = async () => {
  try {
    const cartData = await AsyncStorage.getItem(CART_STORAGE_KEY);
    const totalData = await AsyncStorage.getItem(CART_TOTAL_STORAGE_KEY);

    const cart = cartData ? JSON.parse(cartData) : [];
    const total = totalData ? JSON.parse(totalData) : 0;

    console.log('Cart loaded from storage:', cart.length, 'items, total:', total);
    return { cart, total };
  } catch (error) {
    console.error('Error loading cart from storage:', error);
    return { cart: [], total: 0 };
  }
};

// Create a store with Zustand
const useStore = create((set, get) => ({
  // User state
  user: null,
  isAuthenticated: false,
  isAdmin: false, // Flag to check if user is admin

  // Products state
  products: SAMPLE_PRODUCTS,
  filteredProducts: SAMPLE_PRODUCTS,
  selectedProduct: null,

  // Log initial products (for debugging)
  logInitialProducts: () => {
    console.log('Initial products in store:', SAMPLE_PRODUCTS);
    return SAMPLE_PRODUCTS;
  },

  // Orders state
  orders: SAMPLE_ORDERS,
  filteredOrders: SAMPLE_ORDERS,
  selectedOrder: null,

  // Notifications state
  notifications: SAMPLE_NOTIFICATIONS,
  unreadNotificationsCount: SAMPLE_NOTIFICATIONS.filter(n => !n.isRead).length,

  // Cart state
  cart: [],
  cartTotal: 0,

  // Loading state
  isLoading: false,

  // Error state
  error: null,

  // Initialization
  initializeCart: async () => {
    const { cart, total } = await loadCartFromStorage();
    set({ cart, cartTotal: total });
  },

  // Actions

  // Auth actions
  login: (userData) => set({
    user: userData,
    isAuthenticated: true,
    isAdmin: userData.email === '<EMAIL>' || userData.role === 'admin' // Check for admin
  }),
  logout: async () => {
    try {
      // Import the signOut function from supabase.js
      const { signOut } = require('../lib/supabase');

      // Call the Supabase signOut function to end the session
      const { error } = await signOut();

      if (error) {
        console.error('Error signing out:', error.message);
      }

      // Get current cart before clearing state
      const currentCart = get().cart;
      const currentTotal = get().cartTotal;

      // Clear the user state regardless of whether the API call succeeded
      set({
        user: null,
        isAuthenticated: false,
        isAdmin: false,
      });

      // Note: We're not clearing the cart here to preserve it between sessions
    } catch (error) {
      console.error('Error in logout function:', error);

      // Get current cart before clearing state
      const currentCart = get().cart;
      const currentTotal = get().cartTotal;

      // Clear the user state even if there was an error
      set({
        user: null,
        isAuthenticated: false,
        isAdmin: false,
      });

      // Note: We're not clearing the cart here to preserve it between sessions
    }
  },
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),

  // Product actions
  setProducts: (products) => set({ products, filteredProducts: products }),
  setSelectedProduct: (product) => set({ selectedProduct: product }),
  filterProducts: (categoryId) => set((state) => ({
    filteredProducts: categoryId
      ? state.products.filter(product => product.category === categoryId)
      : state.products
  })),
  searchProducts: (query) => set((state) => ({
    filteredProducts: state.products.filter(product =>
      product.name.toLowerCase().includes(query.toLowerCase()) ||
      product.description.toLowerCase().includes(query.toLowerCase())
    )
  })),

  // Cart actions
  addToCart: (product, quantity = 1) => set((state) => {
    const existingItem = state.cart.find(item => item.id === product.id);

    let newCart;
    if (existingItem) {
      newCart = state.cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + quantity }
          : item
      );
    } else {
      newCart = [...state.cart, { ...product, quantity }];
    }

    const total = newCart.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    // Save cart to storage
    saveCartToStorage(newCart, total);

    return { cart: newCart, cartTotal: total };
  }),

  removeFromCart: (productId) => set((state) => {
    const newCart = state.cart.filter(item => item.id !== productId);

    const total = newCart.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    // Save cart to storage
    saveCartToStorage(newCart, total);

    return { cart: newCart, cartTotal: total };
  }),

  updateCartItemQuantity: (productId, quantity) => set((state) => {
    const newCart = state.cart.map(item =>
      item.id === productId
        ? { ...item, quantity: Math.max(1, quantity) }
        : item
    );

    const total = newCart.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    // Save cart to storage
    saveCartToStorage(newCart, total);

    return { cart: newCart, cartTotal: total };
  }),

  clearCart: () => {
    // Clear cart in storage
    saveCartToStorage([], 0);

    return set({ cart: [], cartTotal: 0 });
  },

  // Order actions
  setOrders: (orders) => set({ orders, filteredOrders: orders }),
  setSelectedOrder: (order) => set({ selectedOrder: order }),
  filterOrders: (status) => set((state) => ({
    filteredOrders: status
      ? state.orders.filter(order => order.status === status)
      : state.orders
  })),
  updateOrderStatus: (orderId, status) => set((state) => ({
    orders: state.orders.map(order =>
      order.id === orderId
        ? { ...order, status, updatedAt: new Date().toISOString() }
        : order
    ),
    filteredOrders: state.filteredOrders.map(order =>
      order.id === orderId
        ? { ...order, status, updatedAt: new Date().toISOString() }
        : order
    ),
  })),

  // Notification actions
  markNotificationAsRead: (notificationId) => set((state) => {
    const updatedNotifications = state.notifications.map(notification =>
      notification.id === notificationId
        ? { ...notification, isRead: true }
        : notification
    );

    return {
      notifications: updatedNotifications,
      unreadNotificationsCount: updatedNotifications.filter(n => !n.isRead).length
    };
  }),

  markAllNotificationsAsRead: () => set((state) => ({
    notifications: state.notifications.map(notification => ({ ...notification, isRead: true })),
    unreadNotificationsCount: 0
  })),

  deleteNotification: (notificationId) => set((state) => {
    const updatedNotifications = state.notifications.filter(
      notification => notification.id !== notificationId
    );

    return {
      notifications: updatedNotifications,
      unreadNotificationsCount: updatedNotifications.filter(n => !n.isRead).length
    };
  }),

  // Admin Product actions
  addProduct: (product) => set((state) => {
    const newProduct = {
      ...product,
      id: (state.products.length + 1).toString(), // Simple ID generation for demo
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return {
      products: [...state.products, newProduct],
      filteredProducts: [...state.filteredProducts, newProduct],
    };
  }),

  updateProduct: (productId, updatedProduct) => set((state) => ({
    products: state.products.map(product =>
      product.id === productId
        ? {
            ...product,
            ...updatedProduct,
            updatedAt: new Date().toISOString()
          }
        : product
    ),
    filteredProducts: state.filteredProducts.map(product =>
      product.id === productId
        ? {
            ...product,
            ...updatedProduct,
            updatedAt: new Date().toISOString()
          }
        : product
    ),
  })),

  deleteProduct: (productId) => set((state) => ({
    products: state.products.filter(product => product.id !== productId),
    filteredProducts: state.filteredProducts.filter(product => product.id !== productId),
  })),

  // UI actions
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
}));

export default useStore;
