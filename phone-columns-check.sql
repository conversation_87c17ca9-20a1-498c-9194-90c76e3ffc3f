-- SQL script to identify tables and columns that might contain phone numbers

-- Check information schema for columns with names suggesting phone numbers
SELECT 
    table_schema,
    table_name,
    column_name,
    data_type
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND (
        column_name ILIKE '%phone%' OR
        column_name ILIKE '%mobile%' OR
        column_name ILIKE '%cell%' OR
        column_name ILIKE '%contact%' OR
        column_name ILIKE '%tel%'
    )
ORDER BY 
    table_name, column_name;

-- Sample data from profiles table
SELECT 
    id, 
    full_name,
    email,
    phone,
    mobile,
    contact_number
FROM 
    profiles
LIMIT 5;

-- Sample data from orders table with potential phone fields
SELECT 
    id,
    user_id,
    delivery_phone,
    phone
FROM 
    orders
LIMIT 5;

-- Sample data from addresses table with potential phone fields
SELECT 
    id,
    user_id,
    phone,
    mobile,
    contact_number
FROM 
    addresses
LIMIT 5;

-- Join orders with profiles to see the relationship
SELECT 
    o.id as order_id,
    o.user_id,
    p.id as profile_id,
    p.full_name,
    p.email,
    p.phone as profile_phone,
    o.delivery_phone as order_phone
FROM 
    orders o
LEFT JOIN 
    profiles p ON o.user_id = p.id
LIMIT 5;

-- Join orders with addresses to see the relationship
SELECT 
    o.id as order_id,
    o.address_id,
    a.id as address_id,
    a.user_id,
    a.phone as address_phone,
    o.delivery_phone as order_phone
FROM 
    orders o
LEFT JOIN 
    addresses a ON o.address_id = a.id
LIMIT 5;
