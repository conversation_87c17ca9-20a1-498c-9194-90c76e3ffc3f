{"name": "zakumunda", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.12", "@react-navigation/native": "^7.1.8", "@react-navigation/native-stack": "^7.3.12", "@supabase/supabase-js": "^2.49.4", "base64-js": "^1.5.1", "expo": "~53.0.7", "expo-font": "^13.3.1", "expo-image-picker": "^16.1.4", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "formik": "^2.4.6", "prop-types": "^15.8.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-fast-image": "^8.6.3", "react-native-image-zoom-viewer": "^3.0.1", "react-native-paper": "^5.14.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-size-matters": "^0.4.2", "react-native-snap-carousel": "^3.9.1", "react-native-svg": "^15.11.2", "react-native-swipe-list-view": "^3.2.9", "react-native-web": "^0.20.0", "yup": "^1.6.1", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}