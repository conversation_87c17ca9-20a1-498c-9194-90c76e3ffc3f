-- Create an RPC function to insert addresses with security definer
CREATE OR <PERSON><PERSON>LACE FUNCTION insert_address(
  p_user_id UUID,
  p_address_line1 TEXT,
  p_city TEXT,
  p_country TEXT DEFAULT 'Zambia',
  p_is_default BOOLEAN DEFAULT FALSE,
  p_phone TEXT DEFAULT NULL
)
<PERSON><PERSON><PERSON>NS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_address_id UUID;
BEGIN
  -- Insert the address
  INSERT INTO addresses (
    user_id,
    address_line1,
    city,
    country,
    is_default,
    phone,
    created_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_address_line1,
    p_city,
    p_country,
    p_is_default,
    p_phone,
    NOW(),
    NOW()
  )
  RETURNING id INTO v_address_id;
  
  -- Return the new address ID
  RETURN v_address_id;
END;
$$;
